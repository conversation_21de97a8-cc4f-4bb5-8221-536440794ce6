#!/usr/bin/env python3
"""
Test script to verify that all functions work after login
"""

import sys
import time
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

def test_functions_after_login():
    """Test that all functions are available after login"""
    print("🧪 Testing Functions After Login")
    print("=" * 50)
    
    # Create QApplication
    app = QApplication(sys.argv)
    
    # Import login window
    try:
        from gui_pyqt5.login_window_fixed import LoginWindowFixed
        print("✅ Login window imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import login window: {e}")
        return False
    
    # Create login window
    login_window = LoginWindowFixed()
    print("✅ Login window created")
    
    # Track test results
    test_results = {
        'main_window_created': False,
        'camera_button_available': False,
        'ai_buttons_available': False,
        'functions_enabled': False
    }
    
    def on_authentication_successful():
        """Handle successful authentication"""
        print("🔐 Authentication successful signal received")
        
        # Check main window and functions after delay
        def check_functions():
            if hasattr(login_window, 'main_app') and login_window.main_app:
                main_window = login_window.main_app
                test_results['main_window_created'] = True
                print("✅ Main window created successfully")
                
                # Check if camera button exists and is functional
                if hasattr(main_window, 'camera_btn'):
                    test_results['camera_button_available'] = True
                    print("✅ Camera button available")
                    
                    # Check if AI detection buttons exist
                    ai_buttons = ['expression_btn', 'age_btn', 'object_btn', 'anomaly_btn']
                    ai_buttons_found = 0
                    for btn_name in ai_buttons:
                        if hasattr(main_window, btn_name):
                            ai_buttons_found += 1
                    
                    if ai_buttons_found == len(ai_buttons):
                        test_results['ai_buttons_available'] = True
                        print("✅ All AI detection buttons available")
                        
                        # Check if functions are properly enabled/disabled
                        # Initially buttons should be disabled until camera starts
                        if (hasattr(main_window, 'expression_btn') and 
                            not main_window.expression_btn.isEnabled()):
                            test_results['functions_enabled'] = True
                            print("✅ Functions properly initialized (disabled until camera starts)")
                        else:
                            print("⚠️ Function states may not be properly managed")
                    else:
                        print(f"❌ Only {ai_buttons_found}/{len(ai_buttons)} AI buttons found")
                else:
                    print("❌ Camera button not found")
            else:
                print("❌ Main window not created")
            
            # Exit test
            QTimer.singleShot(500, app.quit)
        
        # Check after main window should be created
        QTimer.singleShot(2000, check_functions)
    
    # Connect authentication signal
    login_window.authentication_successful.connect(on_authentication_successful)
    
    # Show login window
    login_window.show()
    print("✅ Login window shown")
    
    # Simulate login after a short delay
    def simulate_login():
        print("🔄 Simulating login...")
        login_window.username_entry.setText("admin")
        login_window.password_entry.setText("password123")
        login_window.authenticate()
    
    QTimer.singleShot(500, simulate_login)
    
    # Run application
    print("🚀 Starting application...")
    app.exec_()
    
    # Report results
    print("\n📊 Test Results:")
    print(f"   Main window created: {'✅' if test_results['main_window_created'] else '❌'}")
    print(f"   Camera button available: {'✅' if test_results['camera_button_available'] else '❌'}")
    print(f"   AI buttons available: {'✅' if test_results['ai_buttons_available'] else '❌'}")
    print(f"   Functions properly initialized: {'✅' if test_results['functions_enabled'] else '❌'}")
    
    all_passed = all(test_results.values())
    if all_passed:
        print("✅ All functions are working correctly after login!")
        return True
    else:
        print("❌ Some functions have issues after login")
        return False

if __name__ == "__main__":
    success = test_functions_after_login()
    sys.exit(0 if success else 1)
