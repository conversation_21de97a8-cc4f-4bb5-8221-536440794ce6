#!/usr/bin/env python3
"""
Simple login test without GUI
"""

import sys
import os

# Add current directory to path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_authentication_logic():
    """Test just the authentication logic"""
    try:
        print("🧪 Testing authentication logic...")
        
        # Test config
        try:
            from utils.config import Config
            config = Config()
            print(f"✅ Config imported from utils.config")
        except ImportError:
            print(f"⚠️ Using fallback config")
            class Config:
                DEFAULT_USERNAME = "admin"
                DEFAULT_PASSWORD = "password123"
            config = Config()
        
        print(f"📋 Expected username: '{config.DEFAULT_USERNAME}'")
        print(f"📋 Expected password: '{config.DEFAULT_PASSWORD}'")
        
        # Test various inputs
        test_cases = [
            ("admin", "password123", True),
            ("Admin", "password123", True),  # Case insensitive username
            ("ADMIN", "password123", True),
            ("admin", "Password123", False),  # Case sensitive password
            ("admin", "wrong", False),
            ("wrong", "password123", False),
            ("", "", False),
            ("admin", "", False),
            ("", "password123", False),
        ]
        
        print(f"\n🔐 Testing authentication cases:")
        for username, password, expected in test_cases:
            # Simulate the authentication logic
            result = (username.lower() == config.DEFAULT_USERNAME.lower() and
                     password == config.DEFAULT_PASSWORD)
            
            status = "✅" if result == expected else "❌"
            print(f"   {status} '{username}' / '{password}' -> {result} (expected {expected})")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🛡️ AI Video Detection - Simple Login Test")
    print("=" * 50)
    
    result = test_authentication_logic()
    print(f"\n📊 Test result: {'✅ PASSED' if result else '❌ FAILED'}")
