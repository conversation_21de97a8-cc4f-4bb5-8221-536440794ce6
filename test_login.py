#!/usr/bin/env python3
"""
Test script for login functionality
"""

import sys
import os

# Add current directory to path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_login_basic():
    """Test basic login functionality"""
    try:
        print("🧪 Testing login functionality...")
        
        # Import required modules
        from PyQt5.QtWidgets import QApplication
        from gui_pyqt5.login_window import LoginWindow
        
        # Create application
        app = QApplication(sys.argv)
        
        # Create login window
        login = LoginWindow()
        
        # Test configuration
        print(f"✅ Login window created successfully")
        print(f"📋 Config username: '{login.config.DEFAULT_USERNAME}'")
        print(f"📋 Config password: '{login.config.DEFAULT_PASSWORD}'")
        print(f"📋 Username field: '{login.username_entry.text()}'")
        print(f"📋 Password field: '{login.password_entry.text()}'")
        
        # Test authentication logic manually
        username = login.username_entry.text().strip()
        password = login.password_entry.text().strip()
        
        print(f"\n🔐 Testing authentication logic:")
        print(f"   Username: '{username}' == '{login.config.DEFAULT_USERNAME}' ? {username.lower() == login.config.DEFAULT_USERNAME.lower()}")
        print(f"   Password: '{password}' == '{login.config.DEFAULT_PASSWORD}' ? {password == login.config.DEFAULT_PASSWORD}")
        
        # Test authentication method
        print(f"\n🧪 Testing authenticate() method...")
        
        # Connect to authentication signal
        def on_auth_success():
            print("✅ Authentication signal received!")
            app.quit()
        
        login.authentication_successful.connect(on_auth_success)
        
        # Show window
        login.show()
        
        # Simulate login button click after a delay
        from PyQt5.QtCore import QTimer
        def simulate_login():
            print("🔄 Simulating login button click...")
            login.authenticate()
        
        QTimer.singleShot(1000, simulate_login)
        
        # Run for a short time
        QTimer.singleShot(3000, app.quit)
        
        return app.exec_()
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return 1

def test_config():
    """Test configuration loading"""
    try:
        print("\n🧪 Testing configuration...")
        
        # Test config import
        try:
            from utils.config import Config
            config = Config()
            print(f"✅ Config imported from utils.config")
        except ImportError:
            print(f"⚠️ Using fallback config")
            class Config:
                DEFAULT_USERNAME = "admin"
                DEFAULT_PASSWORD = "password123"
            config = Config()
        
        print(f"📋 Username: '{config.DEFAULT_USERNAME}'")
        print(f"📋 Password: '{config.DEFAULT_PASSWORD}'")
        
        return True
        
    except Exception as e:
        print(f"❌ Config test failed: {e}")
        return False

if __name__ == "__main__":
    print("🛡️ AI Video Detection - Login Test")
    print("=" * 50)
    
    # Test configuration
    config_ok = test_config()
    
    if config_ok:
        # Test login
        result = test_login_basic()
        print(f"\n📊 Test result: {'✅ PASSED' if result == 0 else '❌ FAILED'}")
    else:
        print(f"\n📊 Test result: ❌ FAILED (Config issues)")
