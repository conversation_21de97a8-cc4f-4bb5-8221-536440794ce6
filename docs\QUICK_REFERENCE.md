# AI Video Detection - Quick Reference Guide

## 🚀 Quick Start

### Launch Application
```bash
python main_pyqt5.py
```

### Default Login
- **Username**: `admin`
- **Password**: `password123`

## 🎨 New UI Features at a Glance

### 📊 Real-Time Status Panel
Located in the main window sidebar:
- **📹 Camera**: Shows camera connection status
- **🧠 AI Models**: Displays AI model loading status  
- **🔍 Detection**: Real-time detection activity
- **📼 Recording**: Current recording status
- **📊 Performance**: Live FPS, processing time, detection count

### 🔔 Professional Notifications
Automatic notifications appear in the top-right corner:
- **✅ Success**: Green notifications for successful operations
- **⚠️ Warning**: Yellow notifications for warnings
- **❌ Error**: Red notifications for errors
- **ℹ️ Info**: Blue notifications for information

### 🎯 Interactive Elements
- **Hover Effects**: Buttons and cards respond to mouse hover
- **Loading States**: Visual feedback during operations
- **Progress Bars**: Smooth animated progress tracking
- **Status Dots**: Animated indicators for system status

## ⌨️ Keyboard Shortcuts

### Navigation
| Key | Action |
|-----|--------|
| `Tab` | Next element |
| `Shift+Tab` | Previous element |
| `Enter` | Activate button/control |
| `Space` | Toggle checkbox/button |
| `Escape` | Cancel/Close |

### Accessibility
| Key | Action |
|-----|--------|
| `Ctrl+Alt+H` | Toggle high contrast |
| `Ctrl++` | Increase font size |
| `Ctrl+-` | Decrease font size |
| `Ctrl+0` | Reset font size |
| `F1` | Show help |

### Application
| Key | Action |
|-----|--------|
| `Ctrl+Q` | Quit application |
| `Alt+F4` | Close window |
| `Ctrl+Tab` | Switch between panels |

## 🎛️ Main Interface Layout

### Left Panel - Video Display
- **Camera Feed**: Live video stream
- **Detection Overlays**: AI detection results
- **Recording Indicator**: Shows when recording

### Right Panel - Controls
1. **Real-Time Status**: System status monitoring
2. **Statistics**: Detection statistics and metrics
3. **Detection Controls**: Enable/disable AI features
4. **Action Buttons**: Camera, recording, settings

### Bottom Panel - Status Bar
- **System Messages**: Current operation status
- **Connection Status**: Camera and AI model status
- **Performance Info**: FPS and processing metrics

## 🤖 AI Detection Features

### Facial Expression Detection
- **Enable**: Click "😊 Expression Detection" button
- **Status**: Check real-time status panel
- **Results**: View overlays on video feed

### Age Detection
- **Enable**: Click "👤 Age Detection" button  
- **Accuracy**: Displays confidence percentage
- **Range**: Shows estimated age range

### Object Detection
- **Enable**: Click "📦 Object Detection" button
- **Categories**: Detects multiple object types
- **Confidence**: Shows detection confidence

### Anomaly Detection
- **Enable**: Click "⚠️ Anomaly Detection" button
- **Alerts**: Automatic notifications for anomalies
- **Logging**: Records all anomaly events

## 📊 Dashboard Features

### Access Dashboard
- **Method 1**: Click "📊 Dashboard" in main window
- **Method 2**: Use menu: View → Dashboard

### Dashboard Sections
1. **Statistics Cards**: Key metrics overview
2. **Activity Log**: Real-time event logging
3. **Performance Charts**: System performance graphs
4. **Export Options**: PDF reports and CSV data

### Export Data
- **PDF Report**: Click "📄 Generate PDF Report"
- **CSV Export**: Click "📊 Export CSV Data"
- **Location**: Files saved to `reports/` and `exports/` folders

## 🎨 Theme & Appearance

### Theme Options
- **Light Theme**: Default professional light mode
- **Dark Theme**: Modern dark mode (future feature)
- **High Contrast**: Accessibility mode (`Ctrl+Alt+H`)

### Font Scaling
- **Increase**: `Ctrl++` (up to 200%)
- **Decrease**: `Ctrl+-` (down to 80%)
- **Reset**: `Ctrl+0` (back to 100%)

## 🔧 Settings & Configuration

### Camera Settings
- **Source**: Select camera device
- **Resolution**: Choose video resolution
- **FPS**: Set frame rate

### AI Model Settings
- **Confidence Threshold**: Adjust detection sensitivity
- **Model Selection**: Choose AI models to load
- **Performance Mode**: Balance accuracy vs speed

### Recording Settings
- **Output Format**: Video file format
- **Quality**: Recording quality settings
- **Location**: Save directory

## 🆘 Troubleshooting

### Common Issues

#### Camera Not Working
1. Check camera connection
2. Verify camera permissions
3. Try different camera index
4. Restart application

#### AI Detection Not Working
1. Ensure models are downloaded
2. Check AI status in real-time panel
3. Verify sufficient system resources
4. Check error notifications

#### Performance Issues
1. Lower video resolution
2. Reduce AI detection frequency
3. Close other applications
4. Check system resources

#### UI Issues
1. Restart application
2. Check PyQt5 installation
3. Try basic mode if available
4. Check system compatibility

### Error Messages

#### "Camera Failed"
- **Cause**: Camera not accessible
- **Solution**: Check connections and permissions

#### "AI Models Loading"
- **Cause**: Models still initializing
- **Solution**: Wait for completion notification

#### "Recording Error"
- **Cause**: Insufficient disk space or permissions
- **Solution**: Check storage and file permissions

## 📞 Support

### Getting Help
- **In-App**: Press `F1` for help
- **Documentation**: Check `docs/` folder
- **Logs**: Review `logs/` folder for errors

### System Requirements
- **OS**: Windows 10/11, macOS 10.14+, Linux
- **Python**: 3.8 or higher
- **RAM**: 4GB minimum, 8GB recommended
- **Storage**: 2GB free space
- **Camera**: USB or built-in camera

### Dependencies
```bash
pip install PyQt5 opencv-python numpy pillow
```

## 🎯 Tips & Best Practices

### Performance Optimization
1. **Close unused applications** for better performance
2. **Use lower resolution** for older hardware
3. **Enable only needed AI features** to save resources
4. **Regular cleanup** of recorded files

### Best User Experience
1. **Use keyboard shortcuts** for faster navigation
2. **Enable notifications** for important alerts
3. **Check status panel** for system health
4. **Regular exports** for data backup

### Accessibility
1. **Enable high contrast** for better visibility
2. **Increase font size** if needed
3. **Use keyboard navigation** for hands-free operation
4. **Screen reader support** available

---

*For detailed information, see the complete Enhanced UI Guide in `docs/ENHANCED_UI_GUIDE.md`*
