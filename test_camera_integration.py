#!/usr/bin/env python3
"""
Test Camera Integration
Test the enhanced camera integration in the PyQt5 application
"""

import sys
import os
import time

# Add project root to path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def test_enhanced_camera_manager():
    """Test the enhanced camera manager directly"""
    print("🔍 TESTING ENHANCED CAMERA MANAGER")
    print("=" * 50)
    
    try:
        from camera_diagnostic import EnhancedCameraManager
        
        # Create camera manager
        camera_manager = EnhancedCameraManager()
        
        # Test initialization
        print("1. Testing camera initialization...")
        if camera_manager.initialize_camera():
            print("   ✅ Camera initialization: SUCCESS")
            
            # Get camera info
            camera_info = camera_manager.get_camera_info()
            print(f"   📊 Camera info: {camera_info}")
            
            # Test frame reading
            print("\n2. Testing frame reading...")
            success_count = 0
            for i in range(10):
                ret, frame = camera_manager.read_frame()
                if ret and frame is not None:
                    success_count += 1
                    if i % 3 == 0:
                        print(f"   Frame {i+1}: ✅ ({frame.shape})")
                else:
                    print(f"   Frame {i+1}: ❌")
                    
            print(f"\n   📊 Frame reading success rate: {success_count}/10 ({success_count*10}%)")
            
            # Test availability check
            print("\n3. Testing availability check...")
            if camera_manager.is_available():
                print("   ✅ Camera availability: Available")
            else:
                print("   ❌ Camera availability: Not available")
                
            # Clean up
            camera_manager.release()
            print("   ✅ Camera released successfully")
            
            return True
            
        else:
            print("   ❌ Camera initialization: FAILED")
            return False
            
    except Exception as e:
        print(f"   ❌ Error testing enhanced camera manager: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_pyqt5_integration():
    """Test PyQt5 integration (without actually starting the GUI)"""
    print("\n🔍 TESTING PYQT5 INTEGRATION")
    print("=" * 50)
    
    try:
        # Test imports
        print("1. Testing imports...")
        try:
            from gui_pyqt5.main_window import EnhancedMainWindow
            print("   ✅ PyQt5 main window import: SUCCESS")
        except ImportError as e:
            print(f"   ⚠️ PyQt5 main window import: FAILED - {e}")
            print("   Note: This is expected if PyQt5 is not installed")
            return False
            
        # Test enhanced camera manager import in PyQt5 context
        print("\n2. Testing enhanced camera manager import...")
        try:
            from camera_diagnostic import EnhancedCameraManager
            print("   ✅ Enhanced camera manager import: SUCCESS")
        except ImportError as e:
            print(f"   ❌ Enhanced camera manager import: FAILED - {e}")
            return False
            
        print("\n   ✅ PyQt5 integration test: PASSED")
        print("   Note: Full GUI test requires running the actual application")
        return True
        
    except Exception as e:
        print(f"   ❌ Error testing PyQt5 integration: {e}")
        return False

def test_fallback_compatibility():
    """Test fallback compatibility for systems without enhanced camera manager"""
    print("\n🔍 TESTING FALLBACK COMPATIBILITY")
    print("=" * 50)
    
    try:
        import cv2
        
        print("1. Testing basic OpenCV camera access...")
        cap = cv2.VideoCapture(0)
        
        if cap.isOpened():
            ret, frame = cap.read()
            if ret and frame is not None:
                print("   ✅ Basic OpenCV camera: Working")
                print(f"   📐 Resolution: {frame.shape}")
            else:
                print("   ❌ Basic OpenCV camera: Can't read frames")
        else:
            print("   ❌ Basic OpenCV camera: Can't open")
            
        cap.release()
        
        print("\n2. Testing fallback camera manager...")
        # Simulate the fallback class from the PyQt5 integration
        class FallbackCameraManager:
            def __init__(self):
                self.video_cap = None
            def initialize_camera(self):
                self.video_cap = cv2.VideoCapture(0)
                return self.video_cap.isOpened()
            def read_frame(self):
                if self.video_cap:
                    return self.video_cap.read()
                return False, None
            def release(self):
                if self.video_cap:
                    self.video_cap.release()
            def is_available(self):
                return self.video_cap is not None and self.video_cap.isOpened()
                
        fallback_manager = FallbackCameraManager()
        
        if fallback_manager.initialize_camera():
            print("   ✅ Fallback camera manager: Working")
            
            ret, frame = fallback_manager.read_frame()
            if ret and frame is not None:
                print("   ✅ Fallback frame reading: Working")
            else:
                print("   ❌ Fallback frame reading: Failed")
                
            fallback_manager.release()
        else:
            print("   ❌ Fallback camera manager: Failed")
            
        return True
        
    except Exception as e:
        print(f"   ❌ Error testing fallback compatibility: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 CAMERA INTEGRATION TEST SUITE")
    print("=" * 60)
    
    results = []
    
    # Test 1: Enhanced camera manager
    results.append(("Enhanced Camera Manager", test_enhanced_camera_manager()))
    
    # Test 2: PyQt5 integration
    results.append(("PyQt5 Integration", test_pyqt5_integration()))
    
    # Test 3: Fallback compatibility
    results.append(("Fallback Compatibility", test_fallback_compatibility()))
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 TEST RESULTS SUMMARY")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
            
    print(f"\nOverall: {passed}/{total} tests passed ({(passed/total)*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 ALL TESTS PASSED! Camera integration is working correctly.")
        print("\n🔧 NEXT STEPS:")
        print("1. Run the PyQt5 application: python main_pyqt5.py")
        print("2. Test camera functionality in the GUI")
        print("3. Verify AI detection pipeline works with camera")
    else:
        print(f"\n⚠️ {total - passed} test(s) failed. Check the errors above.")
        print("\n🔧 TROUBLESHOOTING:")
        print("1. Ensure camera is connected and not used by other apps")
        print("2. Check camera permissions in Windows Settings")
        print("3. Install missing dependencies if needed")
        
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
