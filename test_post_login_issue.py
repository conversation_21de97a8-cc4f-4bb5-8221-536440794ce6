#!/usr/bin/env python3
"""
Test script to demonstrate the post-login issue where functions don't work
"""

import sys
import time
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

def test_post_login_flow():
    """Test the complete login to main window flow"""
    print("🧪 Testing Post-Login Flow")
    print("=" * 50)
    
    # Create QApplication
    app = QApplication(sys.argv)
    
    # Import login window
    try:
        from gui_pyqt5.login_window_fixed import LoginWindowFixed
        print("✅ Login window imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import login window: {e}")
        return False
    
    # Create login window
    login_window = LoginWindowFixed()
    print("✅ Login window created")
    
    # Track application state
    main_window_created = False
    main_window_shown = False
    
    def on_authentication_successful():
        """Handle successful authentication"""
        print("🔐 Authentication successful signal received")
        
        # Check if main window gets created
        def check_main_window():
            nonlocal main_window_created, main_window_shown
            if hasattr(login_window, 'main_app') and login_window.main_app:
                main_window_created = True
                print("✅ Main window created successfully")
                if login_window.main_app.isVisible():
                    main_window_shown = True
                    print("✅ Main window is visible")
                else:
                    print("❌ Main window created but not visible")
            else:
                print("❌ Main window not created")

        # Check multiple times with longer delays
        QTimer.singleShot(2000, check_main_window)
        QTimer.singleShot(3000, check_main_window)

        # Exit test after checking
        QTimer.singleShot(4000, app.quit)
    
    # Connect authentication signal
    login_window.authentication_successful.connect(on_authentication_successful)
    
    # Show login window
    login_window.show()
    print("✅ Login window shown")
    
    # Simulate login after a short delay
    def simulate_login():
        print("🔄 Simulating login...")
        login_window.username_entry.setText("admin")
        login_window.password_entry.setText("password123")
        login_window.authenticate()
    
    QTimer.singleShot(500, simulate_login)
    
    # Run application
    print("🚀 Starting application...")
    app.exec_()
    
    # Report results
    print("\n📊 Test Results:")
    print(f"   Main window created: {'✅' if main_window_created else '❌'}")
    print(f"   Main window shown: {'✅' if main_window_shown else '❌'}")
    
    if main_window_created and main_window_shown:
        print("✅ Post-login flow works correctly")
        return True
    else:
        print("❌ Post-login flow has issues")
        return False

if __name__ == "__main__":
    success = test_post_login_flow()
    sys.exit(0 if success else 1)
