#!/usr/bin/env python3
"""
Test Video Display Pipeline
Test the complete video display pipeline from camera to GUI
"""

import sys
import os
import time
import threading

# Add project root to path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def test_camera_to_display_pipeline():
    """Test the complete camera to display pipeline"""
    print("🔍 TESTING CAMERA TO DISPLAY PIPELINE")
    print("=" * 50)
    
    try:
        from camera_diagnostic import EnhancedCameraManager
        import cv2
        import numpy as np
        
        # Test camera initialization
        print("1. Testing camera initialization...")
        camera_manager = EnhancedCameraManager()
        
        if not camera_manager.initialize_camera():
            print("   ❌ Camera initialization failed")
            return False
            
        print("   ✅ Camera initialized successfully")
        
        # Test frame capture and processing
        print("\n2. Testing frame capture and processing...")
        frames_captured = 0
        frames_processed = 0
        
        for i in range(10):
            ret, frame = camera_manager.read_frame()
            if ret and frame is not None:
                frames_captured += 1
                
                # Test frame processing (similar to what GUI does)
                try:
                    # Validate frame
                    if frame.size > 0:
                        height, width = frame.shape[:2]
                        
                        # Test brightness enhancement for dark frames
                        mean_intensity = cv2.mean(frame)[0]
                        if mean_intensity < 5:
                            frame = cv2.convertScaleAbs(frame, alpha=2.0, beta=30)
                            print(f"   Frame {i+1}: Enhanced dark frame (intensity: {mean_intensity:.1f})")
                        
                        # Test resize
                        frame_resized = cv2.resize(frame, (640, 480))
                        
                        # Test BGR to RGB conversion
                        if len(frame_resized.shape) == 3 and frame_resized.shape[2] == 3:
                            frame_rgb = cv2.cvtColor(frame_resized, cv2.COLOR_BGR2RGB)
                            
                            # Ensure contiguous array
                            if not frame_rgb.flags['C_CONTIGUOUS']:
                                frame_rgb = np.ascontiguousarray(frame_rgb)
                                
                            frames_processed += 1
                            if i % 3 == 0:
                                print(f"   Frame {i+1}: ✅ Processed ({width}x{height} -> {frame_rgb.shape})")
                        else:
                            print(f"   Frame {i+1}: ⚠️ Unexpected format: {frame.shape}")
                            
                except Exception as e:
                    print(f"   Frame {i+1}: ❌ Processing failed: {e}")
            else:
                print(f"   Frame {i+1}: ❌ Capture failed")
                
        print(f"\n   📊 Results: {frames_captured}/10 captured, {frames_processed}/10 processed")
        
        camera_manager.release()
        
        success_rate = (frames_processed / 10) * 100
        return success_rate >= 80  # At least 80% success
        
    except Exception as e:
        print(f"   ❌ Error testing pipeline: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_qt_image_conversion():
    """Test Qt image conversion without GUI"""
    print("\n🔍 TESTING QT IMAGE CONVERSION")
    print("=" * 50)
    
    try:
        from camera_diagnostic import EnhancedCameraManager
        import cv2
        import numpy as np
        
        # Test without actually importing PyQt5 (to avoid GUI requirements)
        print("1. Testing image format conversion...")
        
        camera_manager = EnhancedCameraManager()
        
        if not camera_manager.initialize_camera():
            print("   ❌ Camera initialization failed")
            return False
            
        # Capture a test frame
        ret, frame = camera_manager.read_frame()
        if not ret or frame is None:
            print("   ❌ Failed to capture test frame")
            camera_manager.release()
            return False
            
        print("   ✅ Test frame captured")
        
        # Test frame processing steps
        try:
            # Step 1: Resize
            frame_resized = cv2.resize(frame, (640, 480))
            print(f"   ✅ Resize: {frame.shape} -> {frame_resized.shape}")
            
            # Step 2: BGR to RGB conversion
            if len(frame_resized.shape) == 3 and frame_resized.shape[2] == 3:
                frame_rgb = cv2.cvtColor(frame_resized, cv2.COLOR_BGR2RGB)
                print(f"   ✅ BGR to RGB: {frame_resized.shape} -> {frame_rgb.shape}")
            else:
                frame_rgb = frame_resized
                print(f"   ✅ Using frame as-is: {frame_rgb.shape}")
                
            # Step 3: Ensure contiguous array
            if not frame_rgb.flags['C_CONTIGUOUS']:
                frame_rgb = np.ascontiguousarray(frame_rgb)
                print("   ✅ Made array contiguous")
            else:
                print("   ✅ Array already contiguous")
                
            # Step 4: Validate for Qt conversion
            height, width = frame_rgb.shape[:2]
            if len(frame_rgb.shape) == 3:
                bytes_per_line = 3 * width
                print(f"   ✅ RGB format ready: {width}x{height}, {bytes_per_line} bytes/line")
            elif len(frame_rgb.shape) == 2:
                bytes_per_line = width
                print(f"   ✅ Grayscale format ready: {width}x{height}, {bytes_per_line} bytes/line")
            else:
                print(f"   ❌ Unsupported format: {frame_rgb.shape}")
                camera_manager.release()
                return False
                
            print("   ✅ All conversion steps successful")
            
        except Exception as e:
            print(f"   ❌ Conversion failed: {e}")
            camera_manager.release()
            return False
            
        camera_manager.release()
        return True
        
    except Exception as e:
        print(f"   ❌ Error testing Qt conversion: {e}")
        return False

def test_signal_emission_simulation():
    """Test signal emission simulation"""
    print("\n🔍 TESTING SIGNAL EMISSION SIMULATION")
    print("=" * 50)
    
    try:
        from camera_diagnostic import EnhancedCameraManager
        import cv2
        
        print("1. Testing frame emission simulation...")
        
        camera_manager = EnhancedCameraManager()
        
        if not camera_manager.initialize_camera():
            print("   ❌ Camera initialization failed")
            return False
            
        # Simulate the video loop behavior
        frames_emitted = 0
        
        for i in range(5):
            ret, frame = camera_manager.read_frame()
            if ret and frame is not None:
                # Simulate what the video loop does
                try:
                    # Validate frame before "emitting"
                    if frame is not None and frame.size > 0:
                        display_frame = frame.copy()
                        
                        # Simulate successful emission
                        frames_emitted += 1
                        print(f"   Frame {i+1}: ✅ Would emit frame {display_frame.shape}")
                    else:
                        print(f"   Frame {i+1}: ❌ Invalid frame, would not emit")
                        
                except Exception as e:
                    print(f"   Frame {i+1}: ❌ Emission simulation failed: {e}")
            else:
                print(f"   Frame {i+1}: ❌ Frame capture failed")
                
        print(f"\n   📊 Emission simulation: {frames_emitted}/5 frames would be emitted")
        
        camera_manager.release()
        return frames_emitted >= 4  # At least 4/5 successful
        
    except Exception as e:
        print(f"   ❌ Error testing signal emission: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 VIDEO DISPLAY PIPELINE TEST SUITE")
    print("=" * 60)
    
    results = []
    
    # Test 1: Camera to display pipeline
    results.append(("Camera to Display Pipeline", test_camera_to_display_pipeline()))
    
    # Test 2: Qt image conversion
    results.append(("Qt Image Conversion", test_qt_image_conversion()))
    
    # Test 3: Signal emission simulation
    results.append(("Signal Emission Simulation", test_signal_emission_simulation()))
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 VIDEO DISPLAY PIPELINE TEST RESULTS")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
            
    print(f"\nOverall: {passed}/{total} tests passed ({(passed/total)*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 VIDEO DISPLAY PIPELINE: READY!")
        print("\n✅ All pipeline components working:")
        print("• Camera capture and frame processing")
        print("• BGR to RGB conversion")
        print("• Frame formatting for Qt display")
        print("• Signal emission simulation")
        print("\n🚀 Ready to test in the actual PyQt5 application!")
        
        print("\n🔧 NEXT STEPS:")
        print("1. Run: python main_pyqt5.py")
        print("2. Click 'Start Camera' button")
        print("3. Check console output for detailed frame processing logs")
        print("4. Verify video feed appears in the application window")
    else:
        print(f"\n⚠️ {total - passed} test(s) failed.")
        print("Video display pipeline needs attention.")
        
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
