#!/usr/bin/env python3
"""
Performance Optimization System for AI Video Detection Application
Provides memory management, frame rate optimization, and processing bottleneck resolution.
"""

import gc
import time
import threading
import psutil
import os
from typing import Dict, List, Optional, Tuple
from datetime import datetime
import numpy as np

class PerformanceOptimizer:
    """
    Comprehensive performance optimization system
    """
    
    def __init__(self):
        self.performance_metrics = {
            'frame_rate': {'current': 0.0, 'target': 30.0, 'average': 0.0},
            'memory_usage': {'current': 0.0, 'peak': 0.0, 'threshold': 80.0},
            'cpu_usage': {'current': 0.0, 'peak': 0.0, 'threshold': 85.0},
            'detection_latency': {'current': 0.0, 'average': 0.0, 'target': 0.1},
            'gpu_usage': {'current': 0.0, 'available': False}
        }
        
        self.optimization_settings = {
            'adaptive_quality': True,
            'frame_skipping': False,  # Temporarily disabled for testing
            'memory_cleanup_interval': 30.0,  # seconds
            'detection_interval_adaptive': True,
            'max_detection_interval': 10,  # frames (reduced for testing)
            'min_detection_interval': 3,   # frames (reduced for testing)
        }
        
        self.frame_history = []
        self.max_history = 100
        self.last_cleanup = time.time()
        self.lock = threading.Lock()
        
        # Performance monitoring thread
        self.monitoring_active = True
        self.monitor_thread = threading.Thread(target=self._monitor_performance, daemon=True)
        self.monitor_thread.start()
        
        print("⚡ Performance optimizer initialized")

    def optimize_frame_processing(self, frame: np.ndarray, detection_enabled: bool = True) -> Tuple[np.ndarray, Dict]:
        """
        Optimize frame processing based on current system performance
        Returns: (optimized_frame, optimization_info)
        """
        start_time = time.time()
        optimization_info = {
            'original_size': frame.shape,
            'optimized_size': frame.shape,
            'quality_reduction': 0,
            'skipped': False,
            'processing_time': 0.0
        }
        
        try:
            with self.lock:
                # Check if frame should be skipped
                if self._should_skip_frame():
                    optimization_info['skipped'] = True
                    return frame, optimization_info
                
                # Adaptive quality reduction
                optimized_frame = self._apply_adaptive_quality(frame)
                optimization_info['optimized_size'] = optimized_frame.shape
                
                # Calculate quality reduction
                original_pixels = frame.shape[0] * frame.shape[1]
                optimized_pixels = optimized_frame.shape[0] * optimized_frame.shape[1]
                optimization_info['quality_reduction'] = 1.0 - (optimized_pixels / original_pixels)
                
                # Update frame history
                self._update_frame_history(start_time)
                
                optimization_info['processing_time'] = time.time() - start_time
                return optimized_frame, optimization_info
                
        except Exception as e:
            print(f"⚠️ Frame optimization error: {e}")
            return frame, optimization_info

    def get_optimal_detection_interval(self) -> int:
        """Get optimal detection interval based on current performance"""
        try:
            cpu_usage = self.performance_metrics['cpu_usage']['current']
            memory_usage = self.performance_metrics['memory_usage']['current']
            frame_rate = self.performance_metrics['frame_rate']['current']
            
            # Base interval
            interval = self.optimization_settings['min_detection_interval']
            
            # Increase interval if system is under stress
            if cpu_usage > 70:
                interval += int((cpu_usage - 70) / 5)
            
            if memory_usage > 70:
                interval += int((memory_usage - 70) / 10)
            
            # Decrease interval if system is performing well
            if cpu_usage < 50 and memory_usage < 50 and frame_rate > 25:
                interval = max(interval - 2, self.optimization_settings['min_detection_interval'])
            
            # Clamp to limits
            interval = max(self.optimization_settings['min_detection_interval'], 
                          min(interval, self.optimization_settings['max_detection_interval']))
            
            return interval
            
        except Exception as e:
            print(f"⚠️ Error calculating detection interval: {e}")
            return self.optimization_settings['min_detection_interval']

    def optimize_memory_usage(self) -> Dict:
        """Optimize memory usage and clean up resources"""
        try:
            memory_before = psutil.virtual_memory().percent
            
            # Force garbage collection
            gc.collect()
            
            # Clear frame history if too large
            with self.lock:
                if len(self.frame_history) > self.max_history:
                    self.frame_history = self.frame_history[-self.max_history//2:]
            
            # Additional cleanup for numpy arrays
            try:
                import numpy as np
                # This helps with numpy memory fragmentation
                np.seterr(all='ignore')
            except:
                pass
            
            memory_after = psutil.virtual_memory().percent
            memory_freed = memory_before - memory_after
            
            self.last_cleanup = time.time()
            
            return {
                'memory_before': memory_before,
                'memory_after': memory_after,
                'memory_freed': memory_freed,
                'cleanup_time': time.time()
            }
            
        except Exception as e:
            print(f"⚠️ Memory optimization error: {e}")
            return {'error': str(e)}

    def _should_skip_frame(self) -> bool:
        """Determine if current frame should be skipped for performance"""
        if not self.optimization_settings['frame_skipping']:
            return False
        
        try:
            cpu_usage = self.performance_metrics['cpu_usage']['current']
            memory_usage = self.performance_metrics['memory_usage']['current']
            
            # Skip if system is heavily loaded
            if cpu_usage > 90 or memory_usage > 90:
                return True
            
            # Skip if frame rate is too low
            current_fps = self.performance_metrics['frame_rate']['current']
            if current_fps < 10:
                return True
            
            return False
            
        except Exception as e:
            print(f"⚠️ Error in frame skip decision: {e}")
            return False

    def _apply_adaptive_quality(self, frame: np.ndarray) -> np.ndarray:
        """Apply adaptive quality reduction based on performance"""
        if not self.optimization_settings['adaptive_quality']:
            return frame
        
        try:
            import cv2
            
            cpu_usage = self.performance_metrics['cpu_usage']['current']
            memory_usage = self.performance_metrics['memory_usage']['current']
            
            # Determine quality reduction factor
            quality_factor = 1.0
            
            if cpu_usage > 80 or memory_usage > 80:
                quality_factor = 0.7  # Reduce to 70%
            elif cpu_usage > 60 or memory_usage > 60:
                quality_factor = 0.85  # Reduce to 85%
            
            if quality_factor < 1.0:
                height, width = frame.shape[:2]
                new_height = int(height * quality_factor)
                new_width = int(width * quality_factor)
                
                # Ensure minimum size
                new_height = max(new_height, 240)
                new_width = max(new_width, 320)
                
                return cv2.resize(frame, (new_width, new_height), interpolation=cv2.INTER_LINEAR)
            
            return frame
            
        except Exception as e:
            print(f"⚠️ Adaptive quality error: {e}")
            return frame

    def _update_frame_history(self, frame_time: float):
        """Update frame processing history"""
        try:
            current_time = time.time()
            
            self.frame_history.append({
                'timestamp': current_time,
                'processing_time': current_time - frame_time
            })
            
            # Limit history size
            if len(self.frame_history) > self.max_history:
                self.frame_history = self.frame_history[-self.max_history:]
            
            # Calculate current FPS
            if len(self.frame_history) >= 2:
                recent_frames = self.frame_history[-10:]  # Last 10 frames
                time_span = recent_frames[-1]['timestamp'] - recent_frames[0]['timestamp']
                if time_span > 0:
                    fps = (len(recent_frames) - 1) / time_span
                    self.performance_metrics['frame_rate']['current'] = fps
                    
                    # Update average
                    if self.performance_metrics['frame_rate']['average'] == 0:
                        self.performance_metrics['frame_rate']['average'] = fps
                    else:
                        self.performance_metrics['frame_rate']['average'] = (
                            self.performance_metrics['frame_rate']['average'] * 0.9 + fps * 0.1
                        )
            
        except Exception as e:
            print(f"⚠️ Frame history update error: {e}")

    def _monitor_performance(self):
        """Background performance monitoring"""
        while self.monitoring_active:
            try:
                # Update system metrics
                self.performance_metrics['cpu_usage']['current'] = psutil.cpu_percent(interval=0.1)
                self.performance_metrics['memory_usage']['current'] = psutil.virtual_memory().percent
                
                # Update peaks
                cpu_current = self.performance_metrics['cpu_usage']['current']
                if cpu_current > self.performance_metrics['cpu_usage']['peak']:
                    self.performance_metrics['cpu_usage']['peak'] = cpu_current
                
                memory_current = self.performance_metrics['memory_usage']['current']
                if memory_current > self.performance_metrics['memory_usage']['peak']:
                    self.performance_metrics['memory_usage']['peak'] = memory_current
                
                # Automatic memory cleanup
                if (time.time() - self.last_cleanup) > self.optimization_settings['memory_cleanup_interval']:
                    if memory_current > 70:  # Only if memory usage is high
                        self.optimize_memory_usage()
                
                time.sleep(1.0)  # Monitor every second
                
            except Exception as e:
                print(f"⚠️ Performance monitoring error: {e}")
                time.sleep(5.0)  # Wait longer on error

    def get_performance_report(self) -> Dict:
        """Get comprehensive performance report"""
        try:
            with self.lock:
                report = {
                    'timestamp': datetime.now(),
                    'metrics': self.performance_metrics.copy(),
                    'optimization_settings': self.optimization_settings.copy(),
                    'recommendations': self._generate_recommendations(),
                    'system_info': {
                        'cpu_count': psutil.cpu_count(),
                        'memory_total': psutil.virtual_memory().total / (1024**3),  # GB
                        'memory_available': psutil.virtual_memory().available / (1024**3),  # GB
                    }
                }
                
                return report
                
        except Exception as e:
            print(f"⚠️ Performance report error: {e}")
            return {'error': str(e)}

    def _generate_recommendations(self) -> List[str]:
        """Generate performance optimization recommendations"""
        recommendations = []
        
        try:
            cpu_usage = self.performance_metrics['cpu_usage']['current']
            memory_usage = self.performance_metrics['memory_usage']['current']
            frame_rate = self.performance_metrics['frame_rate']['current']
            
            if cpu_usage > 80:
                recommendations.append("High CPU usage detected. Consider reducing detection frequency or frame quality.")
            
            if memory_usage > 80:
                recommendations.append("High memory usage detected. Consider enabling more aggressive memory cleanup.")
            
            if frame_rate < 15:
                recommendations.append("Low frame rate detected. Consider reducing video resolution or detection complexity.")
            
            if cpu_usage < 30 and memory_usage < 30 and frame_rate > 25:
                recommendations.append("System performing well. You can increase detection frequency or quality.")
            
            return recommendations
            
        except Exception as e:
            print(f"⚠️ Recommendations error: {e}")
            return ["Error generating recommendations"]

    def cleanup(self):
        """Cleanup performance optimizer"""
        print("🧹 Cleaning up performance optimizer")
        self.monitoring_active = False
        if self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=2.0)
        
        with self.lock:
            self.frame_history.clear()
        
        gc.collect()


# Global optimizer instance
_performance_optimizer = None

def get_performance_optimizer() -> PerformanceOptimizer:
    """Get global performance optimizer instance"""
    global _performance_optimizer
    if _performance_optimizer is None:
        _performance_optimizer = PerformanceOptimizer()
    return _performance_optimizer
