#!/usr/bin/env python3
"""
Comprehensive Diagnostic Test for AI Video Detection Application
Tests all components: camera, models, detection pipeline, and dependencies
"""

import sys
import os
import traceback
from datetime import datetime

def print_header(title):
    """Print formatted header"""
    print("\n" + "="*60)
    print(f"🔍 {title}")
    print("="*60)

def print_result(test_name, success, details=""):
    """Print test result with formatting"""
    status = "✅ PASS" if success else "❌ FAIL"
    print(f"{status} {test_name}")
    if details:
        print(f"    {details}")

def test_basic_dependencies():
    """Test basic Python dependencies"""
    print_header("BASIC DEPENDENCIES TEST")
    
    dependencies = [
        ("Python", sys.version_info >= (3, 7), f"Version: {sys.version}"),
        ("NumPy", True, ""),
        ("OpenCV", True, ""),
        ("PyQt5", True, ""),
        ("Ultralytics (YOLOv8)", True, ""),
        ("PIL/Pillow", True, ""),
    ]
    
    for name, _, version_info in dependencies:
        try:
            if name == "NumPy":
                import numpy as np
                print_result(name, True, f"Version: {np.__version__}")
            elif name == "OpenCV":
                import cv2
                print_result(name, True, f"Version: {cv2.__version__}")
            elif name == "PyQt5":
                from PyQt5.QtWidgets import QApplication
                print_result(name, True, "Available")
            elif name == "Ultralytics (YOLOv8)":
                from ultralytics import YOLO
                print_result(name, True, "Available")
            elif name == "PIL/Pillow":
                from PIL import Image
                print_result(name, True, "Available")
            elif name == "Python":
                print_result(name, True, version_info)
        except ImportError as e:
            print_result(name, False, f"Import error: {e}")
        except Exception as e:
            print_result(name, False, f"Error: {e}")

def test_camera_functionality():
    """Test camera initialization and capture"""
    print_header("CAMERA FUNCTIONALITY TEST")
    
    try:
        import cv2
        
        # Test multiple camera indices
        for idx in range(3):
            try:
                cap = cv2.VideoCapture(idx)
                if cap.isOpened():
                    ret, frame = cap.read()
                    if ret and frame is not None:
                        height, width = frame.shape[:2]
                        print_result(f"Camera {idx}", True, 
                                   f"Resolution: {width}x{height}, Frame shape: {frame.shape}")
                        
                        # Test camera properties
                        fps = cap.get(cv2.CAP_PROP_FPS)
                        print(f"    FPS: {fps}")
                        
                        cap.release()
                        break
                    else:
                        print_result(f"Camera {idx}", False, "Opened but cannot read frames")
                        cap.release()
                else:
                    print_result(f"Camera {idx}", False, "Cannot open")
            except Exception as e:
                print_result(f"Camera {idx}", False, f"Error: {e}")
        
    except ImportError:
        print_result("OpenCV Import", False, "OpenCV not available")

def test_model_files():
    """Test availability and integrity of model files"""
    print_header("MODEL FILES TEST")
    
    model_files = [
        ("YOLOv8 Emotion Model", "models/emotion_detection_83.6_percent.pt"),
        ("Age Detection Prototxt", "models/age_deploy.prototxt"),
        ("Age Detection Model", "models/age_net.caffemodel"),
        ("Face Detector Config", "models/opencv_face_detector.pbtxt"),
        ("Face Detector Model", "models/opencv_face_detector_uint8.pb"),
        ("YOLOv3 Config", "models/yolov3.cfg"),
        ("YOLOv3 Weights", "models/yolov3.weights"),
        ("COCO Names", "models/coco.names"),
        ("Haar Cascade Face", "models/haarcascade_frontalface_default.xml"),
    ]
    
    for name, path in model_files:
        if os.path.exists(path):
            size = os.path.getsize(path)
            size_mb = size / (1024 * 1024)
            print_result(name, True, f"Size: {size_mb:.1f} MB")
        else:
            print_result(name, False, f"File not found: {path}")

def test_detection_modules():
    """Test AI detection module initialization"""
    print_header("DETECTION MODULES TEST")
    
    # Test facial expression detection
    try:
        from detection.custom_yolo_expression import CustomYOLOv8ExpressionDetector
        detector = CustomYOLOv8ExpressionDetector()
        print_result("Facial Expression Detector", True, "CustomYOLOv8ExpressionDetector loaded")
    except Exception as e:
        print_result("Facial Expression Detector", False, f"Error: {e}")
    
    # Test age detection
    try:
        from detection.age_detection import AgeDetector
        age_detector = AgeDetector()
        print_result("Age Detector", True, "AgeDetector loaded")
    except Exception as e:
        print_result("Age Detector", False, f"Error: {e}")
    
    # Test object detection
    try:
        from detection.object_detection import ObjectDetector
        obj_detector = ObjectDetector()
        print_result("Object Detector", True, "ObjectDetector loaded")
    except Exception as e:
        print_result("Object Detector", False, f"Error: {e}")
    
    # Test anomaly detection
    try:
        from detection.anomaly_system import AnomalyDetectionSystem
        anomaly_system = AnomalyDetectionSystem()
        print_result("Anomaly Detection", True, "AnomalyDetectionSystem loaded")
    except Exception as e:
        print_result("Anomaly Detection", False, f"Error: {e}")

def test_gui_components():
    """Test PyQt5 GUI components"""
    print_header("GUI COMPONENTS TEST")
    
    try:
        from PyQt5.QtWidgets import QApplication
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # Test main window import
        try:
            from gui_pyqt5.main_window import EnhancedMainWindow
            print_result("Main Window Import", True, "EnhancedMainWindow available")
        except Exception as e:
            print_result("Main Window Import", False, f"Error: {e}")
        
        # Test login window import
        try:
            from gui_pyqt5.login_window_fixed import LoginWindowFixed
            print_result("Login Window Import", True, "LoginWindowFixed available")
        except Exception as e:
            try:
                from gui_pyqt5.login_window import LoginWindow
                print_result("Login Window Import", True, "LoginWindow available")
            except Exception as e2:
                print_result("Login Window Import", False, f"Error: {e2}")
        
        # Test theme system
        try:
            from gui_pyqt5.theme_system import apply_professional_theme
            print_result("Theme System", True, "Theme system available")
        except Exception as e:
            print_result("Theme System", False, f"Error: {e}")
            
    except Exception as e:
        print_result("PyQt5 Application", False, f"Error: {e}")

def test_database_integration():
    """Test database functionality"""
    print_header("DATABASE INTEGRATION TEST")
    
    try:
        from utils.database_integration import get_database
        db = get_database()
        print_result("Database Connection", True, "Database integration available")
    except Exception as e:
        print_result("Database Connection", False, f"Error: {e}")

def main():
    """Run comprehensive diagnostic tests"""
    print("🛡️ AI Video Detection - Comprehensive Diagnostic Test")
    print(f"📅 Test Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🐍 Python: {sys.version}")
    print(f"📁 Working Directory: {os.getcwd()}")
    
    try:
        test_basic_dependencies()
        test_camera_functionality()
        test_model_files()
        test_detection_modules()
        test_gui_components()
        test_database_integration()
        
        print_header("DIAGNOSTIC SUMMARY")
        print("✅ Diagnostic test completed!")
        print("📋 Review the results above to identify any issues.")
        print("🔧 Failed tests indicate areas that need attention.")
        
    except Exception as e:
        print(f"\n❌ Diagnostic test failed with error: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    main()
