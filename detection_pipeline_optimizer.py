#!/usr/bin/env python3
"""
Detection Pipeline Optimizer and <PERSON><PERSON>r Handler
Provides comprehensive error handling, performance optimization, and recovery mechanisms
for all AI detection modules in the video detection application.
"""

import time
import threading
import traceback
import gc
import psutil
import os
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime
import numpy as np

class DetectionPipelineOptimizer:
    """
    Comprehensive detection pipeline optimizer with error handling and performance monitoring
    """
    
    def __init__(self):
        self.performance_stats = {
            'facial_expression': {'avg_time': 0.0, 'success_rate': 0.0, 'error_count': 0},
            'age_detection': {'avg_time': 0.0, 'success_rate': 0.0, 'error_count': 0},
            'object_detection': {'avg_time': 0.0, 'success_rate': 0.0, 'error_count': 0},
            'anomaly_detection': {'avg_time': 0.0, 'success_rate': 0.0, 'error_count': 0}
        }
        
        self.detection_history = []
        self.max_history = 1000
        self.error_recovery_attempts = {}
        self.max_recovery_attempts = 3
        
        # Performance monitoring (temporarily relaxed for testing)
        self.memory_threshold = 95  # Percentage (increased for testing)
        self.cpu_threshold = 95     # Percentage (increased for testing)
        self.frame_skip_threshold = 5  # Skip frames if processing is too slow
        
        # Thread safety
        self.lock = threading.Lock()
        
        print("🔧 Detection Pipeline Optimizer initialized")

    def optimize_detection_call(self, detector_name: str, detection_func, frame: np.ndarray, **kwargs) -> Tuple[bool, Any]:
        """
        Optimized detection call with error handling and performance monitoring
        """
        start_time = time.time()
        success = False
        result = None
        
        try:
            # Check system resources before processing
            if not self._check_system_resources():
                print(f"⚠️ System resources low, skipping {detector_name} detection")
                return False, None
            
            # Validate input frame
            if not self._validate_frame(frame):
                print(f"❌ Invalid frame for {detector_name}")
                return False, None
            
            # Preprocess frame if needed
            processed_frame = self._preprocess_frame(frame, detector_name)
            
            # Execute detection with timeout
            result = self._execute_with_timeout(detection_func, processed_frame, **kwargs)
            
            if result is not None:
                success = True
                self._update_success_stats(detector_name, time.time() - start_time)
            else:
                self._update_error_stats(detector_name)
                
        except Exception as e:
            print(f"❌ Error in {detector_name}: {e}")
            self._handle_detection_error(detector_name, e)
            self._update_error_stats(detector_name)
            
        finally:
            # Record performance
            processing_time = time.time() - start_time
            self._record_performance(detector_name, processing_time, success)
            
        return success, result

    def _check_system_resources(self) -> bool:
        """Check if system has enough resources for detection"""
        try:
            # Check memory usage
            memory_percent = psutil.virtual_memory().percent
            if memory_percent > self.memory_threshold:
                print(f"⚠️ High memory usage: {memory_percent:.1f}%")
                gc.collect()  # Force garbage collection
                return False
            
            # Check CPU usage
            cpu_percent = psutil.cpu_percent(interval=0.1)
            if cpu_percent > self.cpu_threshold:
                print(f"⚠️ High CPU usage: {cpu_percent:.1f}%")
                return False
                
            return True
            
        except Exception as e:
            print(f"⚠️ Could not check system resources: {e}")
            return True  # Assume OK if we can't check

    def _validate_frame(self, frame: np.ndarray) -> bool:
        """Validate input frame"""
        if frame is None:
            return False
        if frame.size == 0:
            return False
        if len(frame.shape) not in [2, 3]:
            return False
        if frame.shape[0] < 32 or frame.shape[1] < 32:
            return False
        return True

    def _preprocess_frame(self, frame: np.ndarray, detector_name: str) -> np.ndarray:
        """Preprocess frame based on detector requirements"""
        try:
            # Basic preprocessing for all detectors
            processed_frame = frame.copy()
            
            # Detector-specific preprocessing
            if detector_name == 'facial_expression':
                # Ensure RGB format for YOLOv8
                if len(processed_frame.shape) == 3 and processed_frame.shape[2] == 3:
                    # Assume BGR, convert to RGB
                    import cv2
                    processed_frame = cv2.cvtColor(processed_frame, cv2.COLOR_BGR2RGB)
                    
            elif detector_name == 'age_detection':
                # Age detection works better with good contrast
                if len(processed_frame.shape) == 3:
                    import cv2
                    processed_frame = cv2.convertScaleAbs(processed_frame, alpha=1.1, beta=10)
                    
            elif detector_name in ['object_detection', 'anomaly_detection']:
                # YOLO models prefer specific input sizes
                import cv2
                height, width = processed_frame.shape[:2]
                if height > 640 or width > 640:
                    # Resize large frames for better performance
                    scale = 640 / max(height, width)
                    new_width = int(width * scale)
                    new_height = int(height * scale)
                    processed_frame = cv2.resize(processed_frame, (new_width, new_height))
            
            return processed_frame
            
        except Exception as e:
            print(f"⚠️ Preprocessing failed for {detector_name}: {e}")
            return frame

    def _execute_with_timeout(self, func, *args, timeout=5.0, **kwargs):
        """Execute function with timeout"""
        import signal
        
        class TimeoutError(Exception):
            pass
        
        def timeout_handler(signum, frame):
            raise TimeoutError("Detection timeout")
        
        try:
            # Set timeout (Unix/Linux only)
            if hasattr(signal, 'SIGALRM'):
                signal.signal(signal.SIGALRM, timeout_handler)
                signal.alarm(int(timeout))
            
            result = func(*args, **kwargs)
            
            if hasattr(signal, 'SIGALRM'):
                signal.alarm(0)  # Cancel timeout
                
            return result
            
        except TimeoutError:
            print(f"⏰ Detection timeout after {timeout}s")
            return None
        except Exception as e:
            if hasattr(signal, 'SIGALRM'):
                signal.alarm(0)  # Cancel timeout
            raise e

    def _handle_detection_error(self, detector_name: str, error: Exception):
        """Handle detection errors with recovery attempts"""
        with self.lock:
            if detector_name not in self.error_recovery_attempts:
                self.error_recovery_attempts[detector_name] = 0
            
            self.error_recovery_attempts[detector_name] += 1
            
            if self.error_recovery_attempts[detector_name] <= self.max_recovery_attempts:
                print(f"🔄 Attempting recovery for {detector_name} (attempt {self.error_recovery_attempts[detector_name]})")
                
                # Specific recovery strategies
                if "memory" in str(error).lower() or "allocation" in str(error).lower():
                    print("🧹 Memory error detected, forcing garbage collection")
                    gc.collect()
                    
                elif "model" in str(error).lower() or "load" in str(error).lower():
                    print("🔄 Model error detected, may need model reload")
                    
                elif "cuda" in str(error).lower() or "gpu" in str(error).lower():
                    print("🔄 GPU error detected, falling back to CPU")
                    
            else:
                print(f"❌ Max recovery attempts reached for {detector_name}")

    def _update_success_stats(self, detector_name: str, processing_time: float):
        """Update success statistics"""
        with self.lock:
            stats = self.performance_stats[detector_name]
            
            # Update average processing time
            if stats['avg_time'] == 0:
                stats['avg_time'] = processing_time
            else:
                stats['avg_time'] = (stats['avg_time'] * 0.9) + (processing_time * 0.1)
            
            # Reset error count on success
            if detector_name in self.error_recovery_attempts:
                self.error_recovery_attempts[detector_name] = 0

    def _update_error_stats(self, detector_name: str):
        """Update error statistics"""
        with self.lock:
            self.performance_stats[detector_name]['error_count'] += 1

    def _record_performance(self, detector_name: str, processing_time: float, success: bool):
        """Record performance metrics"""
        with self.lock:
            record = {
                'timestamp': datetime.now(),
                'detector': detector_name,
                'processing_time': processing_time,
                'success': success,
                'memory_usage': psutil.virtual_memory().percent,
                'cpu_usage': psutil.cpu_percent()
            }
            
            self.detection_history.append(record)
            
            # Limit history size
            if len(self.detection_history) > self.max_history:
                self.detection_history = self.detection_history[-self.max_history:]

    def get_performance_report(self) -> Dict:
        """Get comprehensive performance report"""
        with self.lock:
            report = {
                'timestamp': datetime.now(),
                'detector_stats': self.performance_stats.copy(),
                'system_stats': {
                    'memory_usage': psutil.virtual_memory().percent,
                    'cpu_usage': psutil.cpu_percent(),
                    'available_memory': psutil.virtual_memory().available / (1024**3),  # GB
                },
                'recent_performance': self.detection_history[-100:] if self.detection_history else []
            }
            
            # Calculate success rates
            for detector_name in self.performance_stats:
                recent_records = [r for r in self.detection_history[-100:] if r['detector'] == detector_name]
                if recent_records:
                    success_count = sum(1 for r in recent_records if r['success'])
                    report['detector_stats'][detector_name]['success_rate'] = success_count / len(recent_records)
                else:
                    report['detector_stats'][detector_name]['success_rate'] = 0.0
            
            return report

    def should_skip_detection(self, detector_name: str) -> bool:
        """Determine if detection should be skipped based on performance"""
        with self.lock:
            # Skip if too many recent errors
            if detector_name in self.error_recovery_attempts:
                if self.error_recovery_attempts[detector_name] >= self.max_recovery_attempts:
                    return True
            
            # Skip if system resources are low
            if not self._check_system_resources():
                return True
                
            return False

    def cleanup(self):
        """Cleanup resources"""
        print("🧹 Cleaning up detection pipeline optimizer")
        with self.lock:
            self.detection_history.clear()
            self.error_recovery_attempts.clear()
        gc.collect()


# Global optimizer instance
_optimizer = None

def get_detection_optimizer() -> DetectionPipelineOptimizer:
    """Get global detection optimizer instance"""
    global _optimizer
    if _optimizer is None:
        _optimizer = DetectionPipelineOptimizer()
    return _optimizer

def optimize_detection(detector_name: str, detection_func, frame: np.ndarray, **kwargs):
    """Convenience function for optimized detection"""
    optimizer = get_detection_optimizer()
    return optimizer.optimize_detection_call(detector_name, detection_func, frame, **kwargs)
