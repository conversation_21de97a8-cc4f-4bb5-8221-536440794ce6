#!/usr/bin/env python3
"""
Test Dashboard from Main Window
Test the dashboard opening functionality from the main window context
"""

import sys
import os

# Add current directory to path for imports
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_dashboard_from_main():
    """Test dashboard opening from main window context"""
    try:
        print("🧪 Testing Dashboard from Main Window Context...")
        print("=" * 60)
        
        # Import PyQt5 components
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        
        # Create QApplication
        app = QApplication(sys.argv)
        app.setAttribute(Qt.AA_EnableHighDpiScaling, True)
        app.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
        
        print("✅ QApplication created successfully")
        
        # Import and create main window (simplified)
        from gui_pyqt5.main_window import EnhancedMainWindow
        print("✅ Main window module imported successfully")
        
        # Create a mock main window with just the dashboard functionality
        class MockMainWindow:
            def __init__(self):
                self.dashboard_window = None
                
            def open_dashboard(self):
                """Open analytics dashboard"""
                try:
                    print("📊 Opening dashboard...")
                    
                    # Check if dashboard already exists and is visible
                    if hasattr(self, 'dashboard_window') and self.dashboard_window is not None:
                        if self.dashboard_window.isVisible():
                            # Bring existing dashboard to front
                            self.dashboard_window.raise_()
                            self.dashboard_window.activateWindow()
                            print("✅ Existing dashboard brought to front")
                            return
                        else:
                            # Dashboard exists but is hidden, show it
                            self.dashboard_window.show()
                            self.dashboard_window.raise_()
                            self.dashboard_window.activateWindow()
                            print("✅ Existing dashboard shown")
                            return
                    
                    # Try to import and create new PyQt5 dashboard
                    try:
                        from gui_pyqt5.dashboard_window import DashboardWindow
                        self.dashboard_window = DashboardWindow()
                        self.dashboard_window.show_dashboard()
                        print("✅ PyQt5 Dashboard opened successfully")
                    except ImportError as e:
                        print(f"⚠️ PyQt5 dashboard import error: {e}")
                        return False
                        
                except Exception as e:
                    print(f"❌ Error opening dashboard: {e}")
                    return False
                    
                return True
        
        # Create mock main window
        main_window = MockMainWindow()
        print("✅ Mock main window created")
        
        # Test dashboard opening
        print("\n🎯 Testing dashboard opening...")
        success = main_window.open_dashboard()
        
        if success:
            print("\n📊 Dashboard Test Results:")
            print(f"   • Dashboard Window: {main_window.dashboard_window}")
            print(f"   • Window Visible: {main_window.dashboard_window.isVisible()}")
            print(f"   • Window Title: {main_window.dashboard_window.windowTitle()}")
            
            print("\n✅ Dashboard should now be visible!")
            print("   • Close the dashboard window to exit this test")
            print("   • Try clicking the dashboard button multiple times to test reuse")
            
            # Test multiple opens
            print("\n🔄 Testing multiple dashboard opens...")
            main_window.open_dashboard()  # Should reuse existing
            main_window.open_dashboard()  # Should reuse existing
            
            # Run the application
            return app.exec_()
        else:
            print("❌ Dashboard test failed")
            return 1
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return 1

if __name__ == "__main__":
    try:
        print("🛡️ AI Video Detection - Dashboard from Main Window Test")
        print("=" * 70)
        
        result = test_dashboard_from_main()
        sys.exit(result)
        
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        sys.exit(1)
