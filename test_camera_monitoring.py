#!/usr/bin/env python3
"""
Test Camera Monitoring System
Test the enhanced camera monitoring and status reporting features
"""

import sys
import os
import time
import threading

# Add project root to path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def test_camera_monitoring():
    """Test camera monitoring features"""
    print("🔍 TESTING CAMERA MONITORING SYSTEM")
    print("=" * 50)
    
    try:
        from camera_diagnostic import EnhancedCameraManager
        
        # Create camera manager
        camera_manager = EnhancedCameraManager()
        
        # Test initialization
        print("1. Initializing camera with monitoring...")
        if not camera_manager.initialize_camera():
            print("   ❌ Camera initialization failed")
            return False
            
        print("   ✅ Camera initialized successfully")
        
        # Test status reporting
        print("\n2. Testing status reporting...")
        status_report = camera_manager.get_status_report()
        print(f"   📊 Initial status: {status_report['status']}")
        print(f"   📊 Camera info: {status_report['camera_info']}")
        print(f"   📊 Health score: {camera_manager.get_health_score():.1f}/100")
        
        # Test frame reading with monitoring
        print("\n3. Testing frame reading with monitoring...")
        frame_count = 0
        start_time = time.time()
        
        for i in range(30):  # Read 30 frames
            ret, frame = camera_manager.read_frame()
            if ret and frame is not None:
                frame_count += 1
                if i % 10 == 0:
                    # Get status update
                    status = camera_manager.get_status_report()
                    health = camera_manager.get_health_score()
                    print(f"   Frame {i+1}: ✅ (Health: {health:.1f}%, FPS: {status['average_fps']:.1f})")
            else:
                print(f"   Frame {i+1}: ❌")
                
            time.sleep(0.033)  # ~30 FPS
            
        end_time = time.time()
        duration = end_time - start_time
        
        # Final status report
        print("\n4. Final status report...")
        final_status = camera_manager.get_status_report()
        final_health = camera_manager.get_health_score()
        
        print(f"   📊 Frames read: {final_status['total_frames_read']}")
        print(f"   📊 Total failures: {final_status['total_failures']}")
        print(f"   📊 Success rate: {final_status['success_rate']:.1f}%")
        print(f"   📊 Average FPS: {final_status['average_fps']:.1f}")
        print(f"   📊 Health score: {final_health:.1f}/100")
        print(f"   📊 Test duration: {duration:.1f}s")
        
        # Test status history
        print("\n5. Testing status history...")
        if len(camera_manager.status_history) > 0:
            print(f"   📊 Status entries: {len(camera_manager.status_history)}")
            recent_statuses = [entry['status'] for entry in camera_manager.status_history[-5:]]
            print(f"   📊 Recent statuses: {recent_statuses}")
        else:
            print("   ⚠️ No status history available")
            
        # Clean up
        camera_manager.release()
        print("\n   ✅ Camera monitoring test completed successfully")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Error testing camera monitoring: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_recovery_simulation():
    """Test camera recovery simulation"""
    print("\n🔍 TESTING CAMERA RECOVERY SIMULATION")
    print("=" * 50)
    
    try:
        from camera_diagnostic import EnhancedCameraManager
        
        # Create camera manager
        camera_manager = EnhancedCameraManager()
        
        # Initialize camera
        print("1. Initializing camera...")
        if not camera_manager.initialize_camera():
            print("   ❌ Camera initialization failed")
            return False
            
        print("   ✅ Camera initialized")
        
        # Simulate some successful frames
        print("\n2. Reading some successful frames...")
        for i in range(5):
            ret, frame = camera_manager.read_frame()
            if ret:
                print(f"   Frame {i+1}: ✅")
            else:
                print(f"   Frame {i+1}: ❌")
                
        # Get initial health score
        initial_health = camera_manager.get_health_score()
        print(f"   📊 Initial health score: {initial_health:.1f}/100")
        
        # Simulate failures by setting consecutive failures
        print("\n3. Simulating consecutive failures...")
        original_failures = camera_manager.consecutive_failures
        camera_manager.consecutive_failures = 8  # Near the threshold
        
        # Try reading frames (should trigger recovery)
        print("   Attempting frame read with simulated failures...")
        ret, frame = camera_manager.read_frame()
        
        if ret:
            print("   ✅ Frame read successful despite simulated failures")
        else:
            print("   ❌ Frame read failed")
            
        # Check if recovery was attempted
        recovery_status = camera_manager.get_status_report()
        print(f"   📊 Recovery attempts: {recovery_status['recovery_attempts']}")
        
        # Restore original state
        camera_manager.consecutive_failures = original_failures
        
        # Final health check
        final_health = camera_manager.get_health_score()
        print(f"   📊 Final health score: {final_health:.1f}/100")
        
        # Clean up
        camera_manager.release()
        print("\n   ✅ Recovery simulation completed")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Error testing recovery simulation: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_continuous_monitoring():
    """Test continuous monitoring in a separate thread"""
    print("\n🔍 TESTING CONTINUOUS MONITORING")
    print("=" * 50)
    
    try:
        from camera_diagnostic import EnhancedCameraManager
        
        # Create camera manager
        camera_manager = EnhancedCameraManager()
        
        # Initialize camera
        print("1. Initializing camera for continuous monitoring...")
        if not camera_manager.initialize_camera():
            print("   ❌ Camera initialization failed")
            return False
            
        print("   ✅ Camera initialized")
        
        # Monitoring function
        def monitor_camera():
            """Monitor camera in background"""
            for i in range(20):  # Monitor for 20 frames
                ret, frame = camera_manager.read_frame()
                time.sleep(0.1)  # 10 FPS
                
        # Start monitoring thread
        print("\n2. Starting continuous monitoring thread...")
        monitor_thread = threading.Thread(target=monitor_camera, daemon=True)
        monitor_thread.start()
        
        # Monitor status while thread runs
        print("   Monitoring status updates...")
        for i in range(5):
            time.sleep(1)
            status = camera_manager.get_status_report()
            health = camera_manager.get_health_score()
            print(f"   Status check {i+1}: {status['status']} (Health: {health:.1f}%, Frames: {status['total_frames_read']})")
            
        # Wait for thread to complete
        monitor_thread.join(timeout=5)
        
        # Final status
        final_status = camera_manager.get_status_report()
        print(f"\n   📊 Final monitoring results:")
        print(f"   📊 Total frames: {final_status['total_frames_read']}")
        print(f"   📊 Success rate: {final_status['success_rate']:.1f}%")
        print(f"   📊 Average FPS: {final_status['average_fps']:.1f}")
        
        # Clean up
        camera_manager.release()
        print("\n   ✅ Continuous monitoring test completed")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Error testing continuous monitoring: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    print("🚀 CAMERA MONITORING TEST SUITE")
    print("=" * 60)
    
    results = []
    
    # Test 1: Basic monitoring
    results.append(("Camera Monitoring", test_camera_monitoring()))
    
    # Test 2: Recovery simulation
    results.append(("Recovery Simulation", test_recovery_simulation()))
    
    # Test 3: Continuous monitoring
    results.append(("Continuous Monitoring", test_continuous_monitoring()))
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 MONITORING TEST RESULTS")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
            
    print(f"\nOverall: {passed}/{total} tests passed ({(passed/total)*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 ALL MONITORING TESTS PASSED!")
        print("\n📊 Camera monitoring system is working correctly:")
        print("• Status tracking and reporting")
        print("• Health score calculation")
        print("• Recovery attempt monitoring")
        print("• Continuous performance metrics")
    else:
        print(f"\n⚠️ {total - passed} test(s) failed.")
        
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
