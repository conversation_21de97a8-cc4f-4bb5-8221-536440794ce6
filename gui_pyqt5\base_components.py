"""
PyQt5 Base Components

This module contains PyQt5 equivalents of custom Tkinter components
used throughout the AI Video Detection application.
"""

import sys
from PyQt5.QtWidgets import (
    QWidget, QPushButton, QLabel, QFrame, QVBoxLayout, QHB<PERSON><PERSON>ayout,
    QGridLayout, QScrollArea, QApplication, QSizePolicy
)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QRect
from PyQt5.QtGui import QPainter, QPen, QBrush, QColor, QFont, QPalette, QLinearGradient


# Modern color scheme for professional appearance
class Colors:
    # Primary colors - Modern blue palette
    PRIMARY = '#2563EB'      # Modern blue
    PRIMARY_LIGHT = '#3B82F6'
    PRIMARY_DARK = '#1D4ED8'

    # Status colors
    SUCCESS = '#10B981'      # Modern green
    SUCCESS_LIGHT = '#34D399'
    SUCCESS_DARK = '#059669'

    WARNING = '#F59E0B'      # Modern amber
    WARNING_LIGHT = '#FBBF24'
    WARNING_DARK = '#D97706'

    DANGER = '#EF4444'       # Modern red
    DANGER_LIGHT = '#F87171'
    DANGER_DARK = '#DC2626'

    INFO = '#06B6D4'         # Modern cyan
    INFO_LIGHT = '#22D3EE'
    INFO_DARK = '#0891B2'

    # Neutral colors - Modern gray palette
    WHITE = '#FFFFFF'
    GRAY_50 = '#F9FAFB'
    GRAY_100 = '#F3F4F6'
    GRAY_200 = '#E5E7EB'
    GRAY_300 = '#D1D5DB'
    GRAY_400 = '#9CA3AF'
    GRAY_500 = '#6B7280'
    GRAY_600 = '#4B5563'
    GRAY_700 = '#374151'
    GRAY_800 = '#1F2937'
    GRAY_900 = '#111827'
    BLACK = '#000000'

    # Background colors
    BACKGROUND = '#F8FAFC'   # Light blue-gray
    BACKGROUND_DARK = '#0F172A'  # Dark slate

    # Legacy aliases for compatibility
    LIGHT = GRAY_100
    DARK = GRAY_800
    SECONDARY = GRAY_500
    GRAY_LIGHT = GRAY_300
    GRAY_DARK = GRAY_600

    # Gradient colors for modern effects
    GRADIENT_START = '#667EEA'
    GRADIENT_END = '#764BA2'

    # Shadow colors
    SHADOW_LIGHT = 'rgba(0, 0, 0, 0.1)'
    SHADOW_MEDIUM = 'rgba(0, 0, 0, 0.15)'
    SHADOW_DARK = 'rgba(0, 0, 0, 0.25)'


class Typography:
    """
    Enterprise-grade typography system with consistent font stack and sizing
    Ensures accessibility compliance and cross-platform compatibility
    """

    # Font stack with Segoe UI as primary and proper fallbacks
    FONT_FAMILY = "'Segoe UI', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif"

    # Typography scale following 8px grid system
    FONT_SIZE_SMALL = 12      # Small text, captions
    FONT_SIZE_BODY = 14       # Body text, buttons
    FONT_SIZE_HEADING = 18    # Section headings
    FONT_SIZE_TITLE = 24      # Page titles
    FONT_SIZE_DISPLAY = 32    # Large display text

    # Font weights
    WEIGHT_REGULAR = 400
    WEIGHT_MEDIUM = 500
    WEIGHT_BOLD = 700

    # Line heights for optimal readability
    LINE_HEIGHT_TIGHT = 1.2   # Headings
    LINE_HEIGHT_NORMAL = 1.4  # Body text
    LINE_HEIGHT_RELAXED = 1.6 # Long form content

    # Text colors with accessibility compliance (WCAG 2.1 AA)
    TEXT_PRIMARY = Colors.GRAY_800     # 4.5:1 contrast ratio
    TEXT_SECONDARY = Colors.GRAY_600   # 4.5:1 contrast ratio
    TEXT_DISABLED = Colors.GRAY_400    # For disabled states
    TEXT_INVERSE = Colors.WHITE        # For dark backgrounds
    TEXT_LINK = Colors.PRIMARY         # Interactive text
    TEXT_SUCCESS = Colors.SUCCESS_DARK # Success messages
    TEXT_WARNING = Colors.WARNING_DARK # Warning messages
    TEXT_ERROR = Colors.DANGER_DARK    # Error messages

    @staticmethod
    def create_font(size_type='body', weight='regular'):
        """
        Create a standardized font with proper fallbacks

        Args:
            size_type: 'small', 'body', 'heading', 'title', 'display'
            weight: 'regular', 'medium', 'bold'

        Returns:
            QFont: Configured font object
        """
        # Size mapping
        size_map = {
            'small': Typography.FONT_SIZE_SMALL,
            'body': Typography.FONT_SIZE_BODY,
            'heading': Typography.FONT_SIZE_HEADING,
            'title': Typography.FONT_SIZE_TITLE,
            'display': Typography.FONT_SIZE_DISPLAY
        }

        # Weight mapping
        weight_map = {
            'regular': Typography.WEIGHT_REGULAR,
            'medium': Typography.WEIGHT_MEDIUM,
            'bold': Typography.WEIGHT_BOLD
        }

        font_size = size_map.get(size_type, Typography.FONT_SIZE_BODY)
        font_weight = weight_map.get(weight, Typography.WEIGHT_REGULAR)

        font = QFont()
        font.setFamily('Segoe UI')
        font.setStyleHint(QFont.SansSerif)
        font.setPointSize(font_size)
        font.setWeight(font_weight)

        return font


class RoundedButton(QPushButton):
    """
    PyQt5 equivalent of the custom Tkinter RoundedButton class.
    Creates a button with rounded corners and hover effects.
    """
    
    def __init__(self, text="", parent=None, bg_color='#3498DB', fg_color='white', 
                 width=120, height=40, corner_radius=10, font_family='Arial', 
                 font_size=10, font_weight='bold'):
        super().__init__(text, parent)
        
        # Store properties
        self.bg_color = QColor(bg_color)
        self.fg_color = QColor(fg_color)
        self.corner_radius = corner_radius
        self.enabled_state = True
        self.disabled_bg = QColor('#BDC3C7')
        self.disabled_fg = QColor('#7F8C8D')
        self.hover_bg = self.lighten_color(self.bg_color)
        
        # Set size
        self.setFixedSize(width, height)
        
        # Set font
        font = QFont(font_family, font_size)
        if font_weight == 'bold':
            font.setBold(True)
        self.setFont(font)
        
        # Set cursor
        self.setCursor(Qt.PointingHandCursor)
        
        # Apply initial styling
        self.update_style()
        
        # Enable hover events
        self.setAttribute(Qt.WA_Hover, True)
    
    def lighten_color(self, color, factor=30):
        """Lighten a QColor by a given factor"""
        r = min(255, color.red() + factor)
        g = min(255, color.green() + factor)
        b = min(255, color.blue() + factor)
        return QColor(r, g, b)
    
    def update_style(self):
        """Update button styling based on current state with modern design"""
        if self.enabled_state:
            bg = self.bg_color
            fg = self.fg_color
        else:
            bg = self.disabled_bg
            fg = self.disabled_fg

        style = f"""
            QPushButton {{
                background-color: {bg.name()};
                color: {fg.name()};
                border: none;
                border-radius: {self.corner_radius}px;
                padding: 10px 20px;
                font-weight: 600;
            }}
            QPushButton:hover {{
                background-color: {self.hover_bg.name()};
                border-color: {self.lighten_color(self.border_color, 20).name()};
            }}
            QPushButton:pressed {{
                background-color: {self.lighten_color(self.bg_color, -20).name()};
                border-color: {self.lighten_color(self.border_color, -10).name()};
            }}
            QPushButton:disabled {{
                background-color: {self.disabled_bg.name()};
                color: {self.disabled_fg.name()};
                border-color: {self.disabled_bg.name()};
            }}
        """
        self.setStyleSheet(style)
    
    def setEnabled(self, enabled):
        """Override setEnabled to update styling"""
        super().setEnabled(enabled)
        self.enabled_state = enabled
        self.update_style()
    
    def config(self, **kwargs):
        """Configuration method similar to Tkinter"""
        if 'text' in kwargs:
            self.setText(kwargs['text'])
        if 'bg' in kwargs:
            self.bg_color = QColor(kwargs['bg'])
            self.hover_bg = self.lighten_color(self.bg_color)
            self.update_style()
        if 'state' in kwargs:
            enabled = kwargs['state'] != 'disabled'
            self.setEnabled(enabled)


class ColoredButton(QPushButton):
    """
    PyQt5 equivalent of the ColoredButton class from dashboard.
    Provides styled buttons with hover effects and color management.
    """
    
    def __init__(self, text="", parent=None, bg_color='#3498DB', fg_color='white',
                 width=15, height=1, font_family='Arial', font_size=10, font_weight='bold'):
        super().__init__(text, parent)
        
        # Store colors
        self.normal_bg = QColor(bg_color)
        self.fg_color = QColor(fg_color)
        self.hover_bg = self.lighten_color(self.normal_bg)
        
        # Set font
        font = QFont(font_family, font_size)
        if font_weight == 'bold':
            font.setBold(True)
        self.setFont(font)
        
        # Set size (convert from character-based to pixels)
        char_width = 8  # Approximate character width
        char_height = 20  # Approximate character height
        self.setFixedSize(width * char_width, height * char_height + 16)
        
        # Set cursor
        self.setCursor(Qt.PointingHandCursor)
        
        # Apply styling
        self.update_style()
        
        # Enable hover events
        self.setAttribute(Qt.WA_Hover, True)
    
    def lighten_color(self, color, factor=30):
        """Lighten a QColor by a given factor"""
        r = min(255, color.red() + factor)
        g = min(255, color.green() + factor)
        b = min(255, color.blue() + factor)
        return QColor(r, g, b)
    
    def update_style(self):
        """Update button styling"""
        style = f"""
            QPushButton {{
                background-color: {self.normal_bg.name()};
                color: {self.fg_color.name()};
                border: none;
                border-radius: 4px;
                padding: 8px 12px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: {self.hover_bg.name()};
            }}
            QPushButton:pressed {{
                background-color: {self.lighten_color(self.normal_bg, -20).name()};
            }}
        """
        self.setStyleSheet(style)


class ModernButton(QPushButton):
    """
    Modern button with gradient effects and enhanced styling
    Enterprise-grade button component with consistent styling and smooth transitions
    """

    def __init__(self, text="", parent=None, style='primary', size='medium'):
        super().__init__(text, parent)

        self.style_type = style
        self.size_type = size

        # Set size based on type with standardized dimensions
        if size == 'small':
            self.setFixedHeight(32)
            self.padding = '6px 12px'
        elif size == 'large':
            self.setFixedHeight(48)
            self.padding = '12px 24px'
        else:  # medium
            self.setFixedHeight(40)
            self.padding = '8px 16px'

        # Set font using Typography system
        if size == 'small':
            font = Typography.create_font('small', 'medium')
        elif size == 'large':
            font = Typography.create_font('heading', 'medium')
        else:  # medium
            font = Typography.create_font('body', 'medium')

        self.setFont(font)

        # Set cursor
        self.setCursor(Qt.PointingHandCursor)

        # Apply styling
        self.update_modern_style()

        # Enable hover events
        self.setAttribute(Qt.WA_Hover, True)

        # Set minimum width for consistency
        self.setMinimumWidth(80)

    def update_modern_style(self):
        """Apply modern styling based on style type with enterprise-grade consistency"""
        styles = {
            'primary': {
                'bg': Colors.PRIMARY,
                'hover': Colors.PRIMARY_LIGHT,
                'active': Colors.PRIMARY_DARK,
                'text': Colors.WHITE,
                'shadow': Colors.SHADOW_MEDIUM
            },
            'success': {
                'bg': Colors.SUCCESS,
                'hover': Colors.SUCCESS_LIGHT,
                'active': Colors.SUCCESS_DARK,
                'text': Colors.WHITE,
                'shadow': Colors.SHADOW_MEDIUM
            },
            'warning': {
                'bg': Colors.WARNING,
                'hover': Colors.WARNING_LIGHT,
                'active': Colors.WARNING_DARK,
                'text': Colors.WHITE,
                'shadow': Colors.SHADOW_MEDIUM
            },
            'danger': {
                'bg': Colors.DANGER,
                'hover': Colors.DANGER_LIGHT,
                'active': Colors.DANGER_DARK,
                'text': Colors.WHITE,
                'shadow': Colors.SHADOW_MEDIUM
            },
            'secondary': {
                'bg': Colors.GRAY_500,
                'hover': Colors.GRAY_400,
                'active': Colors.GRAY_600,
                'text': Colors.WHITE,
                'shadow': Colors.SHADOW_LIGHT
            },
            'outline': {
                'bg': 'transparent',
                'hover': Colors.GRAY_50,
                'active': Colors.GRAY_100,
                'text': Colors.GRAY_700,
                'border': Colors.GRAY_300,
                'shadow': 'none'
            }
        }

        style_config = styles.get(self.style_type, styles['primary'])

        # Base transition for smooth hover effects (200ms as specified)
        transition = "transition: all 200ms ease-in-out;"

        if self.style_type == 'outline':
            style = f"""
                QPushButton {{
                    background-color: {style_config['bg']};
                    color: {style_config['text']};
                    border: 2px solid {style_config['border']};
                    border-radius: 8px;
                    padding: {self.padding};
                    font-weight: 500;
                    font-family: 'Segoe UI', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
                }}
                QPushButton:hover {{
                    background-color: {style_config['hover']};
                    border-color: {Colors.GRAY_400};
                }}
                QPushButton:pressed {{
                    background-color: {style_config['active']};
                    border-color: {Colors.GRAY_500};
                }}
                QPushButton:disabled {{
                    background-color: {Colors.GRAY_200};
                    color: {Colors.GRAY_400};
                    border-color: {Colors.GRAY_300};
                }}
            """
        else:
            style = f"""
                QPushButton {{
                    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                              stop: 0 {style_config['bg']},
                                              stop: 1 {style_config['active']});
                    color: {style_config['text']};
                    border: none;
                    border-radius: 8px;
                    padding: {self.padding};
                    font-weight: 500;
                    font-family: 'Segoe UI', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
                }}
                QPushButton:hover {{
                    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                              stop: 0 {style_config['hover']},
                                              stop: 1 {style_config['bg']});
                    border: 1px solid {style_config['hover']};
                }}
                QPushButton:pressed {{
                    background: {style_config['active']};
                    border: 1px solid {style_config['active']};
                }}
                QPushButton:disabled {{
                    background: {Colors.GRAY_300};
                    color: {Colors.GRAY_500};
                    border: 1px solid {Colors.GRAY_300};
                }}
            """

        self.setStyleSheet(style)


class LoadingIndicator(QWidget):
    """
    Modern loading indicator with spinner animation
    """

    def __init__(self, parent=None, size=32, color=Colors.PRIMARY):
        super().__init__(parent)

        self.size = size
        self.color = QColor(color)
        self.angle = 0

        # Set widget size
        self.setFixedSize(size, size)

        # Create timer for animation
        self.timer = QTimer()
        self.timer.timeout.connect(self.rotate)

        # Set up the widget
        self.setStyleSheet("background: transparent;")

    def start_animation(self):
        """Start the loading animation"""
        self.timer.start(50)  # Update every 50ms
        self.show()

    def stop_animation(self):
        """Stop the loading animation"""
        self.timer.stop()
        self.hide()

    def rotate(self):
        """Rotate the spinner"""
        self.angle = (self.angle + 10) % 360
        self.update()

    def paintEvent(self, event):
        """Paint the loading spinner"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)

        # Set up the pen
        pen = QPen(self.color)
        pen.setWidth(3)
        pen.setCapStyle(Qt.RoundCap)
        painter.setPen(pen)

        # Draw the spinner
        rect = QRect(3, 3, self.size - 6, self.size - 6)

        # Draw multiple arcs to create spinner effect
        for i in range(8):
            opacity = max(0.1, 1.0 - (i * 0.125))
            color = QColor(self.color)
            color.setAlphaF(opacity)
            pen.setColor(color)
            painter.setPen(pen)

            start_angle = (self.angle + i * 45) * 16  # Qt uses 1/16th degree units
            span_angle = 30 * 16  # 30 degrees

            painter.drawArc(rect, start_angle, span_angle)


class StatusIndicator(QLabel):
    """
    Status indicator with colored dot and text
    """

    def __init__(self, text="", status='neutral', parent=None):
        super().__init__(parent)

        self.status = status
        self.base_text = text

        # Set font
        font = QFont('Segoe UI', 10)
        self.setFont(font)

        # Update display
        self.update_status(status, text)

    def update_status(self, status, text=None):
        """Update the status indicator"""
        self.status = status
        if text:
            self.base_text = text

        # Status colors and icons
        status_config = {
            'success': {'color': Colors.SUCCESS, 'icon': '🟢'},
            'warning': {'color': Colors.WARNING, 'icon': '🟡'},
            'error': {'color': Colors.DANGER, 'icon': '🔴'},
            'info': {'color': Colors.INFO, 'icon': '🔵'},
            'neutral': {'color': Colors.GRAY_500, 'icon': '⚪'}
        }

        config = status_config.get(status, status_config['neutral'])

        # Set text with icon
        self.setText(f"{config['icon']} {self.base_text}")

        # Set color
        self.setStyleSheet(f"""
            QLabel {{
                color: {config['color']};
                background: transparent;
                padding: 4px 8px;
                border-radius: 4px;
            }}
        """)


class ProgressBar(QWidget):
    """
    Modern progress bar with smooth animation
    """

    def __init__(self, parent=None, height=6):
        super().__init__(parent)

        self.progress = 0
        self.height = height
        self.setFixedHeight(height)

        # Animation timer
        self.animation_timer = QTimer()
        self.animation_timer.timeout.connect(self.update_animation)

        # Animation properties
        self.target_progress = 0
        self.animation_speed = 2  # pixels per frame

        self.setStyleSheet("background: transparent;")

    def set_progress(self, value, animate=True):
        """Set progress value (0-100)"""
        self.target_progress = max(0, min(100, value))

        if animate:
            if not self.animation_timer.isActive():
                self.animation_timer.start(16)  # ~60 FPS
        else:
            self.progress = self.target_progress
            self.update()

    def update_animation(self):
        """Update animation frame"""
        if abs(self.progress - self.target_progress) < 1:
            self.progress = self.target_progress
            self.animation_timer.stop()
        else:
            if self.progress < self.target_progress:
                self.progress += self.animation_speed
            else:
                self.progress -= self.animation_speed

        self.update()

    def paintEvent(self, event):
        """Paint the progress bar"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)

        # Background
        bg_rect = QRect(0, 0, self.width(), self.height)
        painter.fillRect(bg_rect, QColor(Colors.GRAY_200))

        # Progress
        if self.progress > 0:
            progress_width = int((self.progress / 100) * self.width())
            progress_rect = QRect(0, 0, progress_width, self.height)

            # Gradient for progress
            gradient = QLinearGradient(0, 0, progress_width, 0)
            gradient.setColorAt(0, QColor(Colors.PRIMARY))
            gradient.setColorAt(1, QColor(Colors.PRIMARY_LIGHT))

            painter.fillRect(progress_rect, gradient)


class StyledFrame(QFrame):
    """
    PyQt5 equivalent for styled frames used throughout the application.
    Provides consistent styling and layout management with enterprise-grade design.
    """

    def __init__(self, parent=None, bg_color='white', border_color=None,
                 border_width=1, border_radius=8):  # Default 8px border-radius
        super().__init__(parent)

        # Apply styling with consistent border-radius
        style = f"background-color: {bg_color};"

        if border_color:
            style += f"border: {border_width}px solid {border_color};"

        # Always apply border-radius for consistency (8px default)
        style += f"border-radius: {border_radius}px;"

        self.setStyleSheet(style)


class StyledLabel(QLabel):
    """
    PyQt5 equivalent for styled labels with consistent formatting using Typography system.
    """

    def __init__(self, text="", parent=None, size_type='body', weight='regular',
                 color=None, bg_color=None):
        super().__init__(text, parent)

        # Set font using Typography system
        font = Typography.create_font(size_type, weight)
        self.setFont(font)

        # Use Typography color if not specified
        if color is None:
            color = Typography.TEXT_PRIMARY

        # Apply styling
        style = f"color: {color};"
        if bg_color:
            style += f"background-color: {bg_color};"

        self.setStyleSheet(style)


# Utility functions for layout management
def create_hbox_layout(*widgets, spacing=5, margins=(0, 0, 0, 0)):
    """Create a horizontal box layout with widgets"""
    layout = QHBoxLayout()
    layout.setSpacing(spacing)
    layout.setContentsMargins(*margins)
    
    for widget in widgets:
        if widget is None:
            layout.addStretch()
        else:
            layout.addWidget(widget)
    
    return layout


def create_vbox_layout(*widgets, spacing=5, margins=(0, 0, 0, 0)):
    """Create a vertical box layout with widgets"""
    layout = QVBoxLayout()
    layout.setSpacing(spacing)
    layout.setContentsMargins(*margins)
    
    for widget in widgets:
        if widget is None:
            layout.addStretch()
        else:
            layout.addWidget(widget)
    
    return layout


def create_grid_layout(widget_grid, spacing=5, margins=(0, 0, 0, 0)):
    """Create a grid layout from a 2D list of widgets"""
    layout = QGridLayout()
    layout.setSpacing(spacing)
    layout.setContentsMargins(*margins)

    for row, row_widgets in enumerate(widget_grid):
        for col, widget in enumerate(row_widgets):
            if widget is not None:
                layout.addWidget(widget, row, col)

    return layout


class NotificationToast(QWidget):
    """
    Professional notification toast with auto-dismiss and animations
    """

    def __init__(self, message, notification_type='info', duration=3000, parent=None):
        super().__init__(parent)

        self.duration = duration
        self.notification_type = notification_type

        # Set up the widget
        self.setWindowFlags(Qt.FramelessWindowHint | Qt.WindowStaysOnTopHint | Qt.Tool)
        self.setAttribute(Qt.WA_TranslucentBackground)
        self.setFixedSize(350, 80)

        # Create layout
        layout = QHBoxLayout(self)
        layout.setContentsMargins(16, 16, 16, 16)
        layout.setSpacing(12)

        # Icon based on type
        icon_map = {
            'success': '✅',
            'warning': '⚠️',
            'error': '❌',
            'info': 'ℹ️'
        }

        # Color scheme based on type
        color_map = {
            'success': {'bg': Colors.SUCCESS, 'text': Colors.WHITE},
            'warning': {'bg': Colors.WARNING, 'text': Colors.WHITE},
            'error': {'bg': Colors.DANGER, 'text': Colors.WHITE},
            'info': {'bg': Colors.INFO, 'text': Colors.WHITE}
        }

        colors = color_map.get(notification_type, color_map['info'])

        # Icon label
        icon_label = QLabel(icon_map.get(notification_type, 'ℹ️'))
        icon_label.setFont(QFont('Segoe UI', 16))
        icon_label.setAlignment(Qt.AlignCenter)
        icon_label.setFixedSize(24, 24)
        layout.addWidget(icon_label)

        # Message label
        message_label = QLabel(message)
        message_label.setFont(Typography.create_font('body', 'medium'))
        message_label.setWordWrap(True)
        message_label.setAlignment(Qt.AlignVCenter)
        layout.addWidget(message_label, 1)

        # Apply styling
        self.setStyleSheet(f"""
            QWidget {{
                background-color: {colors['bg']};
                color: {colors['text']};
                border-radius: 8px;
                border: 1px solid {colors['bg']};
            }}
        """)

        # Auto-dismiss timer
        if duration > 0:
            QTimer.singleShot(duration, self.close)

    def show_at_position(self, x, y):
        """Show toast at specific position"""
        self.move(x, y)
        self.show()
        self.raise_()

    def show_top_right(self, parent_widget=None):
        """Show toast at top-right of parent or screen"""
        if parent_widget:
            parent_rect = parent_widget.geometry()
            x = parent_rect.right() - self.width() - 20
            y = parent_rect.top() + 20
        else:
            screen = QApplication.desktop().screenGeometry()
            x = screen.width() - self.width() - 20
            y = 20

        self.show_at_position(x, y)


class ProfessionalCard(QFrame):
    """
    Professional card component with shadow and hover effects
    """

    def __init__(self, parent=None, title="", content="", elevation=2):
        super().__init__(parent)

        self.elevation = elevation
        self.is_hovered = False

        # Set up the frame
        self.setFrameStyle(QFrame.NoFrame)
        self.setAttribute(Qt.WA_Hover, True)

        # Create layout
        layout = QVBoxLayout(self)
        layout.setContentsMargins(24, 20, 24, 20)  # Professional spacing
        layout.setSpacing(12)

        # Title
        if title:
            title_label = StyledLabel(title, size_type='heading', weight='bold')
            title_label.setStyleSheet(f"color: {Typography.TEXT_PRIMARY}; margin-bottom: 8px;")
            layout.addWidget(title_label)

        # Content
        if content:
            content_label = StyledLabel(content, size_type='body')
            content_label.setWordWrap(True)
            content_label.setStyleSheet(f"color: {Typography.TEXT_SECONDARY};")
            layout.addWidget(content_label)

        # Apply initial styling
        self.update_card_style()

    def update_card_style(self):
        """Update card styling based on hover state"""
        if self.is_hovered:
            border_color = Colors.PRIMARY_LIGHT
            background_color = Colors.GRAY_50
        else:
            border_color = Colors.GRAY_200
            background_color = Colors.WHITE

        self.setStyleSheet(f"""
            QFrame {{
                background-color: {background_color};
                border: 1px solid {border_color};
                border-radius: 12px;
            }}
        """)

    def enterEvent(self, event):
        """Handle mouse enter"""
        self.is_hovered = True
        self.update_card_style()
        super().enterEvent(event)

    def leaveEvent(self, event):
        """Handle mouse leave"""
        self.is_hovered = False
        self.update_card_style()
        super().leaveEvent(event)


class IconButton(QPushButton):
    """
    Professional icon button with consistent styling
    """

    def __init__(self, icon_text="", tooltip="", size='medium', style='ghost', parent=None):
        super().__init__(icon_text, parent)

        self.style_type = style
        self.size_type = size

        # Set size
        if size == 'small':
            self.setFixedSize(32, 32)
            font_size = 12
        elif size == 'large':
            self.setFixedSize(48, 48)
            font_size = 18
        else:  # medium
            self.setFixedSize(40, 40)
            font_size = 14

        # Set font
        font = QFont('Segoe UI', font_size)
        self.setFont(font)

        # Set tooltip
        if tooltip:
            self.setToolTip(tooltip)

        # Set cursor
        self.setCursor(Qt.PointingHandCursor)

        # Apply styling
        self.update_icon_style()

        # Enable hover events
        self.setAttribute(Qt.WA_Hover, True)

    def update_icon_style(self):
        """Update icon button styling"""
        if self.style_type == 'ghost':
            style = f"""
                QPushButton {{
                    background-color: transparent;
                    color: {Colors.GRAY_600};
                    border: none;
                    border-radius: 8px;
                    font-weight: 500;
                }}
                QPushButton:hover {{
                    background-color: {Colors.GRAY_100};
                    color: {Colors.GRAY_800};
                }}
                QPushButton:pressed {{
                    background-color: {Colors.GRAY_200};
                }}
            """
        elif self.style_type == 'primary':
            style = f"""
                QPushButton {{
                    background-color: {Colors.PRIMARY};
                    color: {Colors.WHITE};
                    border: none;
                    border-radius: 8px;
                    font-weight: 500;
                }}
                QPushButton:hover {{
                    background-color: {Colors.PRIMARY_LIGHT};
                }}
                QPushButton:pressed {{
                    background-color: {Colors.PRIMARY_DARK};
                }}
            """
        else:  # outline
            style = f"""
                QPushButton {{
                    background-color: transparent;
                    color: {Colors.GRAY_700};
                    border: 2px solid {Colors.GRAY_300};
                    border-radius: 8px;
                    font-weight: 500;
                }}
                QPushButton:hover {{
                    background-color: {Colors.GRAY_50};
                    border-color: {Colors.GRAY_400};
                }}
                QPushButton:pressed {{
                    background-color: {Colors.GRAY_100};
                }}
            """

        self.setStyleSheet(style)
