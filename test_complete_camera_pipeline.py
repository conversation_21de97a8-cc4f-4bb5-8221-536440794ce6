#!/usr/bin/env python3
"""
Test Complete Camera Pipeline
Test the complete camera functionality from initialization to AI detection processing
"""

import sys
import os
import time
import threading

# Add project root to path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def test_camera_initialization():
    """Test camera initialization with enhanced manager"""
    print("🔍 TESTING CAMERA INITIALIZATION")
    print("=" * 50)
    
    try:
        from camera_diagnostic import EnhancedCameraManager
        
        # Test basic initialization
        print("1. Testing basic camera initialization...")
        camera_manager = EnhancedCameraManager()
        
        if camera_manager.initialize_camera():
            print("   ✅ Camera initialization: SUCCESS")
            camera_info = camera_manager.get_camera_info()
            print(f"   📊 Camera: {camera_info['backend']} - {camera_info['resolution']}")
            camera_manager.release()
        else:
            print("   ❌ Camera initialization: FAILED")
            return False
            
        # Test multiple initialization attempts
        print("\n2. Testing multiple initialization attempts...")
        for i in range(3):
            manager = EnhancedCameraManager()
            success = manager.initialize_camera()
            print(f"   Attempt {i+1}: {'✅' if success else '❌'}")
            manager.release()
            time.sleep(0.5)
            
        return True
        
    except Exception as e:
        print(f"   ❌ Error testing camera initialization: {e}")
        return False

def test_frame_processing():
    """Test frame processing and quality"""
    print("\n🔍 TESTING FRAME PROCESSING")
    print("=" * 50)
    
    try:
        from camera_diagnostic import EnhancedCameraManager
        import cv2
        import numpy as np
        
        camera_manager = EnhancedCameraManager()
        
        print("1. Initializing camera for frame processing...")
        if not camera_manager.initialize_camera():
            print("   ❌ Camera initialization failed")
            return False
            
        print("   ✅ Camera initialized")
        
        # Test frame quality
        print("\n2. Testing frame quality...")
        frames_tested = 0
        valid_frames = 0
        
        for i in range(10):
            ret, frame = camera_manager.read_frame()
            if ret and frame is not None:
                frames_tested += 1
                
                # Check frame properties
                height, width, channels = frame.shape
                
                # Basic quality checks
                if height > 0 and width > 0 and channels == 3:
                    # Check if frame is not completely black or white
                    mean_intensity = np.mean(frame)
                    if 10 < mean_intensity < 245:  # Not too dark or bright
                        valid_frames += 1
                        if i % 3 == 0:
                            print(f"   Frame {i+1}: ✅ ({width}x{height}, intensity: {mean_intensity:.1f})")
                    else:
                        print(f"   Frame {i+1}: ⚠️ Poor quality (intensity: {mean_intensity:.1f})")
                else:
                    print(f"   Frame {i+1}: ❌ Invalid dimensions")
            else:
                print(f"   Frame {i+1}: ❌ Read failed")
                
        quality_rate = (valid_frames / frames_tested * 100) if frames_tested > 0 else 0
        print(f"\n   📊 Frame quality: {valid_frames}/{frames_tested} valid ({quality_rate:.1f}%)")
        
        camera_manager.release()
        return quality_rate >= 80  # At least 80% good quality frames
        
    except Exception as e:
        print(f"   ❌ Error testing frame processing: {e}")
        return False

def test_ai_detection_integration():
    """Test integration with AI detection systems"""
    print("\n🔍 TESTING AI DETECTION INTEGRATION")
    print("=" * 50)
    
    try:
        from camera_diagnostic import EnhancedCameraManager
        
        camera_manager = EnhancedCameraManager()
        
        print("1. Initializing camera for AI detection...")
        if not camera_manager.initialize_camera():
            print("   ❌ Camera initialization failed")
            return False
            
        print("   ✅ Camera initialized")
        
        # Test with face detection (if available)
        print("\n2. Testing face detection integration...")
        try:
            import cv2
            
            # Load face cascade
            face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
            
            faces_detected = 0
            frames_processed = 0
            
            for i in range(10):
                ret, frame = camera_manager.read_frame()
                if ret and frame is not None:
                    frames_processed += 1
                    
                    # Convert to grayscale for face detection
                    gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
                    
                    # Detect faces
                    faces = face_cascade.detectMultiScale(gray, 1.1, 4)
                    
                    if len(faces) > 0:
                        faces_detected += 1
                        if i % 3 == 0:
                            print(f"   Frame {i+1}: ✅ {len(faces)} face(s) detected")
                    else:
                        if i % 3 == 0:
                            print(f"   Frame {i+1}: 📷 No faces detected")
                            
            print(f"   📊 Face detection: {faces_detected}/{frames_processed} frames with faces")
            
        except Exception as e:
            print(f"   ⚠️ Face detection test failed: {e}")
            
        # Test with object detection simulation
        print("\n3. Testing object detection simulation...")
        try:
            objects_detected = 0
            frames_processed = 0
            
            for i in range(5):
                ret, frame = camera_manager.read_frame()
                if ret and frame is not None:
                    frames_processed += 1
                    
                    # Simulate object detection processing
                    # In real implementation, this would call actual AI models
                    height, width = frame.shape[:2]
                    
                    # Simulate detection based on frame properties
                    if width >= 640 and height >= 480:
                        objects_detected += 1
                        print(f"   Frame {i+1}: ✅ Simulated object detection successful")
                    else:
                        print(f"   Frame {i+1}: ⚠️ Frame too small for object detection")
                        
            print(f"   📊 Object detection simulation: {objects_detected}/{frames_processed} successful")
            
        except Exception as e:
            print(f"   ⚠️ Object detection simulation failed: {e}")
            
        camera_manager.release()
        return True
        
    except Exception as e:
        print(f"   ❌ Error testing AI detection integration: {e}")
        return False

def test_performance_metrics():
    """Test camera performance metrics"""
    print("\n🔍 TESTING PERFORMANCE METRICS")
    print("=" * 50)
    
    try:
        from camera_diagnostic import EnhancedCameraManager
        
        camera_manager = EnhancedCameraManager()
        
        print("1. Initializing camera for performance testing...")
        if not camera_manager.initialize_camera():
            print("   ❌ Camera initialization failed")
            return False
            
        print("   ✅ Camera initialized")
        
        # Test sustained frame rate
        print("\n2. Testing sustained frame rate...")
        start_time = time.time()
        frame_count = 0
        target_frames = 60  # Test for 60 frames
        
        for i in range(target_frames):
            ret, frame = camera_manager.read_frame()
            if ret and frame is not None:
                frame_count += 1
            time.sleep(0.033)  # Target ~30 FPS
            
        end_time = time.time()
        duration = end_time - start_time
        actual_fps = frame_count / duration
        
        print(f"   📊 Sustained performance:")
        print(f"   📊 Frames captured: {frame_count}/{target_frames}")
        print(f"   📊 Duration: {duration:.2f}s")
        print(f"   📊 Actual FPS: {actual_fps:.1f}")
        
        # Test health monitoring
        print("\n3. Testing health monitoring...")
        status_report = camera_manager.get_status_report()
        health_score = camera_manager.get_health_score()
        
        print(f"   📊 Health score: {health_score:.1f}/100")
        print(f"   📊 Success rate: {status_report['success_rate']:.1f}%")
        print(f"   📊 Total frames: {status_report['total_frames_read']}")
        print(f"   📊 Total failures: {status_report['total_failures']}")
        
        camera_manager.release()
        
        # Performance criteria
        fps_ok = actual_fps >= 15  # At least 15 FPS
        health_ok = health_score >= 80  # At least 80% health
        success_ok = status_report['success_rate'] >= 90  # At least 90% success
        
        print(f"\n   📊 Performance criteria:")
        print(f"   📊 FPS >= 15: {'✅' if fps_ok else '❌'} ({actual_fps:.1f})")
        print(f"   📊 Health >= 80: {'✅' if health_ok else '❌'} ({health_score:.1f})")
        print(f"   📊 Success >= 90%: {'✅' if success_ok else '❌'} ({status_report['success_rate']:.1f}%)")
        
        return fps_ok and health_ok and success_ok
        
    except Exception as e:
        print(f"   ❌ Error testing performance metrics: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 COMPLETE CAMERA PIPELINE TEST")
    print("=" * 60)
    
    results = []
    
    # Test 1: Camera initialization
    results.append(("Camera Initialization", test_camera_initialization()))
    
    # Test 2: Frame processing
    results.append(("Frame Processing", test_frame_processing()))
    
    # Test 3: AI detection integration
    results.append(("AI Detection Integration", test_ai_detection_integration()))
    
    # Test 4: Performance metrics
    results.append(("Performance Metrics", test_performance_metrics()))
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 COMPLETE PIPELINE TEST RESULTS")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
            
    print(f"\nOverall: {passed}/{total} tests passed ({(passed/total)*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 COMPLETE CAMERA PIPELINE: FULLY FUNCTIONAL!")
        print("\n✅ Your camera system is working perfectly:")
        print("• Enhanced camera manager with robust error handling")
        print("• High-quality frame processing")
        print("• AI detection integration ready")
        print("• Excellent performance metrics")
        print("• Comprehensive monitoring and recovery")
        print("\n🚀 Ready for production use!")
    else:
        print(f"\n⚠️ {total - passed} test(s) failed.")
        print("Camera system needs attention before production use.")
        
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
