#!/usr/bin/env python3
"""
Standalone Camera Test Script
Test camera functionality independently
"""

import cv2
import time
import sys
import os

# Add project root to path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from camera_diagnostic import EnhancedCameraManager

def test_camera_standalone():
    """Test camera functionality standalone"""
    print("🚀 STANDALONE CAMERA TEST")
    print("=" * 40)
    
    # Test enhanced camera manager
    camera_manager = EnhancedCameraManager()
    
    try:
        print("1. Initializing camera...")
        if not camera_manager.initialize_camera():
            print("❌ Failed to initialize camera")
            return False
            
        print("✅ Camera initialized successfully")
        print(f"📊 Camera info: {camera_manager.get_camera_info()}")
        
        print("\n2. Testing frame capture...")
        frame_count = 0
        start_time = time.time()
        
        for i in range(30):  # Test 30 frames
            ret, frame = camera_manager.read_frame()
            if ret and frame is not None:
                frame_count += 1
                if i % 10 == 0:
                    print(f"   Frame {i+1}: ✅ ({frame.shape})")
            else:
                print(f"   Frame {i+1}: ❌")
                
        end_time = time.time()
        fps = frame_count / (end_time - start_time)
        
        print(f"\n📊 Test Results:")
        print(f"   Frames captured: {frame_count}/30")
        print(f"   Average FPS: {fps:.2f}")
        print(f"   Success rate: {(frame_count/30)*100:.1f}%")
        
        if frame_count >= 25:  # 80% success rate
            print("✅ Camera test: PASSED")
            result = True
        else:
            print("❌ Camera test: FAILED")
            result = False
            
    except Exception as e:
        print(f"❌ Camera test error: {e}")
        result = False
        
    finally:
        camera_manager.release()
        
    return result

if __name__ == "__main__":
    success = test_camera_standalone()
    sys.exit(0 if success else 1)
