"""
Professional Notification System for AI Video Detection

This module provides a comprehensive notification system including:
- Toast notifications
- Status alerts
- Progress notifications
- System messages
"""

import sys
from PyQt5.QtWidgets import (
    QWidget, QLabel, QFrame, QVBoxLayout, QHBoxLayout, 
    QApplication, QGraphicsOpacityEffect
)
from PyQt5.QtCore import Qt, QTimer, QPropertyAnimation, QEasingCurve, pyqtSignal
from PyQt5.QtGui import QFont, QPainter, QColor

from .base_components import Colors, Typography, ModernButton, StyledLabel


class NotificationManager:
    """
    Central notification manager for the application
    """
    
    def __init__(self, parent_widget=None):
        self.parent_widget = parent_widget
        self.active_notifications = []
        self.notification_queue = []
        self.max_notifications = 3
    
    def show_success(self, message, title="Success", duration=3000):
        """Show success notification"""
        self._show_notification(message, 'success', title, duration)
    
    def show_warning(self, message, title="Warning", duration=4000):
        """Show warning notification"""
        self._show_notification(message, 'warning', title, duration)
    
    def show_error(self, message, title="Error", duration=5000):
        """Show error notification"""
        self._show_notification(message, 'error', title, duration)
    
    def show_info(self, message, title="Information", duration=3000):
        """Show info notification"""
        self._show_notification(message, 'info', title, duration)
    
    def show_ai_detection(self, detection_type, confidence, details=""):
        """Show AI detection notification"""
        message = f"{detection_type} detected with {confidence:.1f}% confidence"
        if details:
            message += f"\n{details}"
        self._show_notification(message, 'info', "🤖 AI Detection", 2000)
    
    def show_system_status(self, component, status, message=""):
        """Show system status notification"""
        if status == 'online':
            self.show_success(f"{component} is now online", "System Status")
        elif status == 'offline':
            self.show_error(f"{component} is offline", "System Status")
        elif status == 'warning':
            self.show_warning(f"{component}: {message}", "System Status")
    
    def _show_notification(self, message, notification_type, title, duration):
        """Internal method to show notification"""
        if len(self.active_notifications) >= self.max_notifications:
            # Queue the notification
            self.notification_queue.append((message, notification_type, title, duration))
            return
        
        # Create and show notification
        notification = ProfessionalToast(
            message=message,
            notification_type=notification_type,
            title=title,
            duration=duration,
            parent=self.parent_widget
        )
        
        # Position notification
        self._position_notification(notification)
        
        # Add to active list
        self.active_notifications.append(notification)
        
        # Connect close signal
        notification.closed.connect(lambda: self._on_notification_closed(notification))
        
        # Show notification
        notification.show_animated()
    
    def _position_notification(self, notification):
        """Position notification in the stack"""
        if self.parent_widget:
            parent_rect = self.parent_widget.geometry()
            x = parent_rect.right() - notification.width() - 20
            y = parent_rect.top() + 20 + (len(self.active_notifications) * (notification.height() + 10))
        else:
            screen = QApplication.desktop().screenGeometry()
            x = screen.width() - notification.width() - 20
            y = 20 + (len(self.active_notifications) * (notification.height() + 10))
        
        notification.move(x, y)
    
    def _on_notification_closed(self, notification):
        """Handle notification close"""
        if notification in self.active_notifications:
            self.active_notifications.remove(notification)
        
        # Reposition remaining notifications
        self._reposition_notifications()
        
        # Show queued notification if any
        if self.notification_queue:
            message, notification_type, title, duration = self.notification_queue.pop(0)
            self._show_notification(message, notification_type, title, duration)
    
    def _reposition_notifications(self):
        """Reposition all active notifications"""
        for i, notification in enumerate(self.active_notifications):
            if self.parent_widget:
                parent_rect = self.parent_widget.geometry()
                x = parent_rect.right() - notification.width() - 20
                y = parent_rect.top() + 20 + (i * (notification.height() + 10))
            else:
                screen = QApplication.desktop().screenGeometry()
                x = screen.width() - notification.width() - 20
                y = 20 + (i * (notification.height() + 10))
            
            # Animate to new position
            notification.animate_to_position(x, y)


class ProfessionalToast(QWidget):
    """
    Professional toast notification with animations
    """
    
    closed = pyqtSignal()
    
    def __init__(self, message, notification_type='info', title="", duration=3000, parent=None):
        super().__init__(parent)
        
        self.duration = duration
        self.notification_type = notification_type
        
        # Set up the widget
        self.setWindowFlags(Qt.FramelessWindowHint | Qt.WindowStaysOnTopHint | Qt.Tool)
        self.setAttribute(Qt.WA_TranslucentBackground)
        self.setFixedSize(380, 100)
        
        # Create main frame
        self.main_frame = QFrame(self)
        self.main_frame.setGeometry(0, 0, 380, 100)
        
        # Create layout
        layout = QVBoxLayout(self.main_frame)
        layout.setContentsMargins(20, 16, 20, 16)
        layout.setSpacing(8)
        
        # Header with icon and title
        header_layout = QHBoxLayout()
        header_layout.setSpacing(12)
        
        # Icon based on type
        icon_map = {
            'success': '✅',
            'warning': '⚠️',
            'error': '❌',
            'info': 'ℹ️'
        }
        
        # Color scheme based on type
        color_map = {
            'success': {'bg': Colors.SUCCESS, 'text': Colors.WHITE, 'accent': Colors.SUCCESS_LIGHT},
            'warning': {'bg': Colors.WARNING, 'text': Colors.WHITE, 'accent': Colors.WARNING_LIGHT},
            'error': {'bg': Colors.DANGER, 'text': Colors.WHITE, 'accent': Colors.DANGER_LIGHT},
            'info': {'bg': Colors.INFO, 'text': Colors.WHITE, 'accent': Colors.INFO_LIGHT}
        }
        
        self.colors = color_map.get(notification_type, color_map['info'])
        
        # Icon label
        icon_label = QLabel(icon_map.get(notification_type, 'ℹ️'))
        icon_label.setFont(QFont('Segoe UI', 18))
        icon_label.setAlignment(Qt.AlignCenter)
        icon_label.setFixedSize(24, 24)
        header_layout.addWidget(icon_label)
        
        # Title
        if title:
            title_label = QLabel(title)
            title_label.setFont(Typography.create_font('body', 'bold'))
            title_label.setStyleSheet(f"color: {self.colors['text']};")
            header_layout.addWidget(title_label)
        
        header_layout.addStretch()
        
        # Close button
        close_btn = QLabel('✕')
        close_btn.setFont(QFont('Segoe UI', 12))
        close_btn.setAlignment(Qt.AlignCenter)
        close_btn.setFixedSize(20, 20)
        close_btn.setStyleSheet(f"""
            QLabel {{
                color: {self.colors['text']};
                background-color: rgba(255, 255, 255, 0.2);
                border-radius: 10px;
            }}
            QLabel:hover {{
                background-color: rgba(255, 255, 255, 0.3);
            }}
        """)
        close_btn.mousePressEvent = lambda e: self.close_notification()
        header_layout.addWidget(close_btn)
        
        layout.addLayout(header_layout)
        
        # Message
        message_label = QLabel(message)
        message_label.setFont(Typography.create_font('body'))
        message_label.setWordWrap(True)
        message_label.setStyleSheet(f"color: {self.colors['text']};")
        layout.addWidget(message_label)
        
        # Apply styling to main frame
        self.main_frame.setStyleSheet(f"""
            QFrame {{
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                                          stop: 0 {self.colors['bg']},
                                          stop: 1 {self.colors['accent']});
                border-radius: 12px;
                border: 1px solid rgba(255, 255, 255, 0.2);
            }}
        """)
        
        # Set up opacity effect for animations
        self.opacity_effect = QGraphicsOpacityEffect()
        self.setGraphicsEffect(self.opacity_effect)
        self.opacity_effect.setOpacity(0)
        
        # Auto-dismiss timer
        if duration > 0:
            QTimer.singleShot(duration, self.close_notification)
    
    def show_animated(self):
        """Show with slide-in animation"""
        self.show()
        self.raise_()
        
        # Fade in animation
        self.fade_animation = QPropertyAnimation(self.opacity_effect, b"opacity")
        self.fade_animation.setDuration(300)
        self.fade_animation.setStartValue(0)
        self.fade_animation.setEndValue(1)
        self.fade_animation.setEasingCurve(QEasingCurve.OutCubic)
        self.fade_animation.start()
    
    def close_notification(self):
        """Close with fade-out animation"""
        self.fade_out_animation = QPropertyAnimation(self.opacity_effect, b"opacity")
        self.fade_out_animation.setDuration(200)
        self.fade_out_animation.setStartValue(1)
        self.fade_out_animation.setEndValue(0)
        self.fade_out_animation.setEasingCurve(QEasingCurve.InCubic)
        self.fade_out_animation.finished.connect(self._on_fade_out_finished)
        self.fade_out_animation.start()
    
    def _on_fade_out_finished(self):
        """Handle fade out completion"""
        self.closed.emit()
        self.close()
        self.deleteLater()
    
    def animate_to_position(self, x, y):
        """Animate to new position"""
        self.position_animation = QPropertyAnimation(self, b"pos")
        self.position_animation.setDuration(300)
        self.position_animation.setStartValue(self.pos())
        self.position_animation.setEndValue(self.pos().__class__(x, y))
        self.position_animation.setEasingCurve(QEasingCurve.OutCubic)
        self.position_animation.start()


class StatusAlert(QFrame):
    """
    Persistent status alert for important system messages
    """
    
    dismissed = pyqtSignal()
    
    def __init__(self, message, alert_type='info', dismissible=True, parent=None):
        super().__init__(parent)
        
        self.alert_type = alert_type
        self.dismissible = dismissible
        
        # Color scheme
        color_map = {
            'success': {'bg': Colors.SUCCESS_LIGHT, 'border': Colors.SUCCESS, 'text': Colors.SUCCESS_DARK},
            'warning': {'bg': Colors.WARNING_LIGHT, 'border': Colors.WARNING, 'text': Colors.WARNING_DARK},
            'error': {'bg': Colors.DANGER_LIGHT, 'border': Colors.DANGER, 'text': Colors.DANGER_DARK},
            'info': {'bg': Colors.INFO_LIGHT, 'border': Colors.INFO, 'text': Colors.INFO}
        }
        
        colors = color_map.get(alert_type, color_map['info'])
        
        # Set up frame
        self.setFrameStyle(QFrame.NoFrame)
        self.setStyleSheet(f"""
            QFrame {{
                background-color: {colors['bg']};
                border: 2px solid {colors['border']};
                border-radius: 8px;
                padding: 12px;
            }}
        """)
        
        # Create layout
        layout = QHBoxLayout(self)
        layout.setContentsMargins(16, 12, 16, 12)
        layout.setSpacing(12)
        
        # Icon
        icon_map = {
            'success': '✅',
            'warning': '⚠️',
            'error': '❌',
            'info': 'ℹ️'
        }
        
        icon_label = QLabel(icon_map.get(alert_type, 'ℹ️'))
        icon_label.setFont(QFont('Segoe UI', 16))
        icon_label.setStyleSheet(f"color: {colors['text']};")
        layout.addWidget(icon_label)
        
        # Message
        message_label = QLabel(message)
        message_label.setFont(Typography.create_font('body', 'medium'))
        message_label.setWordWrap(True)
        message_label.setStyleSheet(f"color: {colors['text']};")
        layout.addWidget(message_label, 1)
        
        # Dismiss button
        if dismissible:
            dismiss_btn = ModernButton("✕", style='outline', size='small')
            dismiss_btn.setFixedSize(32, 32)
            dismiss_btn.clicked.connect(self.dismiss_alert)
            layout.addWidget(dismiss_btn)
    
    def dismiss_alert(self):
        """Dismiss the alert with animation"""
        self.fade_out_animation = QPropertyAnimation(self, b"maximumHeight")
        self.fade_out_animation.setDuration(300)
        self.fade_out_animation.setStartValue(self.height())
        self.fade_out_animation.setEndValue(0)
        self.fade_out_animation.setEasingCurve(QEasingCurve.InCubic)
        self.fade_out_animation.finished.connect(self._on_dismiss_finished)
        self.fade_out_animation.start()
    
    def _on_dismiss_finished(self):
        """Handle dismiss completion"""
        self.dismissed.emit()
        self.hide()
        self.deleteLater()


# Global notification manager instance
_notification_manager = None

def get_notification_manager(parent_widget=None):
    """Get or create global notification manager"""
    global _notification_manager
    if _notification_manager is None:
        _notification_manager = NotificationManager(parent_widget)
    return _notification_manager

def show_success(message, title="Success", duration=3000):
    """Global function to show success notification"""
    get_notification_manager().show_success(message, title, duration)

def show_warning(message, title="Warning", duration=4000):
    """Global function to show warning notification"""
    get_notification_manager().show_warning(message, title, duration)

def show_error(message, title="Error", duration=5000):
    """Global function to show error notification"""
    get_notification_manager().show_error(message, title, duration)

def show_info(message, title="Information", duration=3000):
    """Global function to show info notification"""
    get_notification_manager().show_info(message, title, duration)
