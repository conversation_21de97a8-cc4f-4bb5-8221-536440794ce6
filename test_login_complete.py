#!/usr/bin/env python3
"""
Comprehensive login test for AI Video Detection
"""

import sys
import os

# Add current directory to path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_login_functionality():
    """Test complete login functionality"""
    try:
        print("🧪 Testing complete login functionality...")
        
        # Import required modules
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import QTimer
        from gui_pyqt5.login_window_fixed import LoginWindowFixed
        
        # Create application
        app = QApplication(sys.argv)
        
        # Create login window
        login = LoginWindowFixed()
        
        print(f"✅ Login window created successfully")
        print(f"📋 Username field: '{login.username_entry.text()}'")
        print(f"📋 Password field: '{login.password_entry.text()}'")
        print(f"📋 Expected username: '{login.config.DEFAULT_USERNAME}'")
        print(f"📋 Expected password: '{login.config.DEFAULT_PASSWORD}'")
        
        # Test authentication logic
        username = login.username_entry.text().strip()
        password = login.password_entry.text().strip()
        
        auth_result = (username.lower() == login.config.DEFAULT_USERNAME.lower() and
                      password == login.config.DEFAULT_PASSWORD)
        
        print(f"🔐 Authentication test: {auth_result}")
        
        # Test different scenarios
        test_cases = [
            ("admin", "password123", True, "Default credentials"),
            ("Admin", "password123", True, "Case insensitive username"),
            ("ADMIN", "password123", True, "Uppercase username"),
            ("admin", "wrong", False, "Wrong password"),
            ("wrong", "password123", False, "Wrong username"),
            ("", "", False, "Empty credentials"),
        ]
        
        print(f"\n🧪 Testing authentication scenarios:")
        for test_user, test_pass, expected, description in test_cases:
            result = (test_user.lower() == login.config.DEFAULT_USERNAME.lower() and
                     test_pass == login.config.DEFAULT_PASSWORD)
            status = "✅" if result == expected else "❌"
            print(f"   {status} {description}: '{test_user}'/'{test_pass}' -> {result}")
        
        # Test UI components
        print(f"\n🎨 Testing UI components:")
        print(f"   ✅ Username field exists: {hasattr(login, 'username_entry')}")
        print(f"   ✅ Password field exists: {hasattr(login, 'password_entry')}")
        print(f"   ✅ Login button exists: {hasattr(login, 'login_button')}")
        print(f"   ✅ Show password checkbox exists: {hasattr(login, 'show_password_check')}")
        
        # Test signal connections
        print(f"\n🔗 Testing signal connections:")
        auth_signal_connected = hasattr(login, 'authentication_successful')
        print(f"   ✅ Authentication signal exists: {auth_signal_connected}")
        
        # Test window properties
        print(f"\n🪟 Testing window properties:")
        print(f"   ✅ Window title: '{login.windowTitle()}'")
        print(f"   ✅ Window size: {login.size().width()}x{login.size().height()}")
        print(f"   ✅ Window is modal: {login.isModal()}")
        
        # Show window briefly for visual verification
        login.show()
        
        # Auto-close after 2 seconds
        QTimer.singleShot(2000, app.quit)
        
        print(f"\n🚀 Showing login window for 2 seconds...")
        app.exec_()
        
        print(f"✅ All tests completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_authentication_method():
    """Test the authentication method directly"""
    try:
        print("\n🔐 Testing authentication method...")
        
        from PyQt5.QtWidgets import QApplication
        from gui_pyqt5.login_window_fixed import LoginWindowFixed
        
        app = QApplication(sys.argv)
        login = LoginWindowFixed()
        
        # Test with correct credentials
        login.username_entry.setText("admin")
        login.password_entry.setText("password123")
        
        print(f"🧪 Testing with correct credentials...")
        
        # Connect to authentication signal
        auth_success = False
        def on_auth_success():
            nonlocal auth_success
            auth_success = True
            print("✅ Authentication signal received!")
        
        login.authentication_successful.connect(on_auth_success)
        
        # Call authenticate method
        login.authenticate()
        
        print(f"📊 Authentication result: {'✅ SUCCESS' if login.authenticated else '❌ FAILED'}")
        print(f"📊 Signal emitted: {'✅ YES' if auth_success else '❌ NO'}")
        
        return login.authenticated and auth_success
        
    except Exception as e:
        print(f"❌ Authentication method test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🛡️ AI Video Detection - Comprehensive Login Test")
    print("=" * 60)
    
    # Test login functionality
    ui_test_passed = test_login_functionality()
    
    # Test authentication method
    auth_test_passed = test_authentication_method()
    
    # Overall result
    overall_result = ui_test_passed and auth_test_passed
    
    print(f"\n" + "=" * 60)
    print(f"📊 TEST RESULTS:")
    print(f"   UI Functionality: {'✅ PASSED' if ui_test_passed else '❌ FAILED'}")
    print(f"   Authentication: {'✅ PASSED' if auth_test_passed else '❌ FAILED'}")
    print(f"   Overall: {'✅ ALL TESTS PASSED' if overall_result else '❌ SOME TESTS FAILED'}")
    print(f"=" * 60)
    
    if overall_result:
        print(f"\n🎉 Login system is working correctly!")
        print(f"📋 Default credentials: admin / password123")
        print(f"🚀 Ready for production use!")
    else:
        print(f"\n⚠️ Some issues detected. Please review the test output.")
    
    sys.exit(0 if overall_result else 1)
