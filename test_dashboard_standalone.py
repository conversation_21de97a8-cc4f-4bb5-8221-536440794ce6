#!/usr/bin/env python3
"""
Standalone Dashboard Test
Test the PyQt5 dashboard window independently to troubleshoot issues
"""

import sys
import os

# Add current directory to path for imports
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_dashboard():
    """Test the dashboard window independently"""
    try:
        print("🧪 Testing PyQt5 Dashboard Window...")
        print("=" * 50)
        
        # Import PyQt5 components
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        
        # Create QApplication
        app = QApplication(sys.argv)
        app.setAttribute(Qt.AA_EnableHighDpiScaling, True)
        app.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
        
        print("✅ QApplication created successfully")
        
        # Import and create dashboard
        from gui_pyqt5.dashboard_window import DashboardWindow
        print("✅ Dashboard module imported successfully")
        
        dashboard = DashboardWindow()
        print("✅ Dashboard window created successfully")
        
        # Show dashboard
        dashboard.show()
        dashboard.raise_()
        dashboard.activateWindow()
        print("✅ Dashboard window displayed")
        
        print("\n🎯 Dashboard Test Results:")
        print(f"   • Window Title: {dashboard.windowTitle()}")
        print(f"   • Window Size: {dashboard.size().width()}x{dashboard.size().height()}")
        print(f"   • Window Visible: {dashboard.isVisible()}")
        print(f"   • Window Active: {dashboard.isActiveWindow()}")
        
        print("\n📊 Dashboard should now be visible!")
        print("   • Close the dashboard window to exit this test")
        print("   • Check for any error messages in the console")
        
        # Run the application
        return app.exec_()
        
    except ImportError as e:
        print(f"❌ Import Error: {e}")
        print("   • Check if PyQt5 is installed: pip install PyQt5")
        print("   • Check if dashboard module exists")
        return 1
        
    except Exception as e:
        print(f"❌ Error: {e}")
        print("   • Check the error details above")
        return 1

def test_basic_pyqt5():
    """Test basic PyQt5 functionality"""
    try:
        print("\n🔧 Testing Basic PyQt5...")
        
        from PyQt5.QtWidgets import QApplication, QMainWindow, QLabel
        from PyQt5.QtCore import Qt
        
        app = QApplication(sys.argv)
        
        window = QMainWindow()
        window.setWindowTitle("PyQt5 Basic Test")
        window.setGeometry(100, 100, 400, 300)
        
        label = QLabel("✅ PyQt5 is working correctly!")
        label.setAlignment(Qt.AlignCenter)
        label.setStyleSheet("font-size: 16px; color: green; padding: 20px;")
        
        window.setCentralWidget(label)
        window.show()
        
        print("✅ Basic PyQt5 test window created")
        print("   • Close the test window to continue")
        
        return app.exec_()
        
    except Exception as e:
        print(f"❌ Basic PyQt5 test failed: {e}")
        return 1

if __name__ == "__main__":
    try:
        print("🛡️ AI Video Detection - Dashboard Test")
        print("=" * 60)
        
        # Test basic PyQt5 first
        print("1️⃣ Testing basic PyQt5 functionality...")
        basic_result = test_basic_pyqt5()
        
        if basic_result == 0:
            print("\n2️⃣ Testing dashboard window...")
            dashboard_result = test_dashboard()
            sys.exit(dashboard_result)
        else:
            print("❌ Basic PyQt5 test failed, skipping dashboard test")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        sys.exit(1)
