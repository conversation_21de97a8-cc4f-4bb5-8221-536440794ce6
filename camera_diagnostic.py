#!/usr/bin/env python3
"""
Camera Diagnostic Tool
Comprehensive camera testing and troubleshooting
"""

import cv2
import sys
import time
import platform
import numpy as np
from typing import List, Dict, Tuple, Optional

class CameraDiagnostic:
    def __init__(self):
        self.results = {}
        self.available_cameras = []
        
    def print_system_info(self):
        """Print system information"""
        print("=" * 60)
        print("🖥️  SYSTEM INFORMATION")
        print("=" * 60)
        print(f"Platform: {platform.platform()}")
        print(f"Python Version: {sys.version}")
        print(f"OpenCV Version: {cv2.__version__}")
        print(f"OpenCV Build Info:")
        print(cv2.getBuildInformation())
        print()
        
    def test_camera_backends(self) -> Dict:
        """Test different camera backends"""
        print("🔧 TESTING CAMERA BACKENDS")
        print("-" * 40)
        
        backends = [
            (cv2.CAP_DSHOW, "DirectShow (Windows)"),
            (cv2.CAP_MSMF, "Media Foundation (Windows)"),
            (cv2.CAP_V4L2, "Video4Linux (Linux)"),
            (cv2.CAP_GSTREAMER, "GStreamer"),
            (cv2.CAP_FFMPEG, "FFmpeg"),
            (cv2.CAP_ANY, "Any Available")
        ]
        
        backend_results = {}
        
        for backend_id, backend_name in backends:
            try:
                print(f"Testing {backend_name}...")
                cap = cv2.VideoCapture(0, backend_id)
                
                if cap.isOpened():
                    ret, frame = cap.read()
                    if ret and frame is not None:
                        height, width = frame.shape[:2]
                        backend_results[backend_name] = {
                            'status': 'SUCCESS',
                            'resolution': f"{width}x{height}",
                            'backend_id': backend_id
                        }
                        print(f"  ✅ {backend_name}: Working ({width}x{height})")
                    else:
                        backend_results[backend_name] = {
                            'status': 'OPENED_NO_FRAME',
                            'backend_id': backend_id
                        }
                        print(f"  ⚠️  {backend_name}: Opened but no frame")
                else:
                    backend_results[backend_name] = {
                        'status': 'FAILED_TO_OPEN',
                        'backend_id': backend_id
                    }
                    print(f"  ❌ {backend_name}: Failed to open")
                    
                cap.release()
                
            except Exception as e:
                backend_results[backend_name] = {
                    'status': 'ERROR',
                    'error': str(e),
                    'backend_id': backend_id
                }
                print(f"  ❌ {backend_name}: Error - {e}")
                
        return backend_results
        
    def scan_camera_indices(self, max_cameras: int = 10) -> List[int]:
        """Scan for available camera indices"""
        print("\n📷 SCANNING CAMERA INDICES")
        print("-" * 40)
        
        available_cameras = []
        
        for i in range(max_cameras):
            try:
                print(f"Testing camera index {i}...")
                cap = cv2.VideoCapture(i)
                
                if cap.isOpened():
                    ret, frame = cap.read()
                    if ret and frame is not None:
                        height, width = frame.shape[:2]
                        available_cameras.append(i)
                        print(f"  ✅ Camera {i}: Available ({width}x{height})")
                        
                        # Get camera properties
                        fps = cap.get(cv2.CAP_PROP_FPS)
                        fourcc = cap.get(cv2.CAP_PROP_FOURCC)
                        print(f"     FPS: {fps}, FOURCC: {fourcc}")
                    else:
                        print(f"  ⚠️  Camera {i}: Opened but no frame")
                else:
                    print(f"  ❌ Camera {i}: Not available")
                    
                cap.release()
                time.sleep(0.1)  # Small delay between tests
                
            except Exception as e:
                print(f"  ❌ Camera {i}: Error - {e}")
                
        self.available_cameras = available_cameras
        return available_cameras
        
    def test_camera_properties(self, camera_index: int = 0) -> Dict:
        """Test camera properties and capabilities"""
        print(f"\n🔍 TESTING CAMERA {camera_index} PROPERTIES")
        print("-" * 40)
        
        properties = {}
        
        try:
            cap = cv2.VideoCapture(camera_index)
            
            if not cap.isOpened():
                print(f"❌ Cannot open camera {camera_index}")
                return properties
                
            # Test basic properties
            props_to_test = [
                (cv2.CAP_PROP_FRAME_WIDTH, "Width"),
                (cv2.CAP_PROP_FRAME_HEIGHT, "Height"),
                (cv2.CAP_PROP_FPS, "FPS"),
                (cv2.CAP_PROP_FOURCC, "FOURCC"),
                (cv2.CAP_PROP_BRIGHTNESS, "Brightness"),
                (cv2.CAP_PROP_CONTRAST, "Contrast"),
                (cv2.CAP_PROP_SATURATION, "Saturation"),
                (cv2.CAP_PROP_HUE, "Hue"),
                (cv2.CAP_PROP_GAIN, "Gain"),
                (cv2.CAP_PROP_EXPOSURE, "Exposure"),
                (cv2.CAP_PROP_BUFFERSIZE, "Buffer Size"),
            ]
            
            for prop_id, prop_name in props_to_test:
                try:
                    value = cap.get(prop_id)
                    properties[prop_name] = value
                    print(f"  {prop_name}: {value}")
                except Exception as e:
                    properties[prop_name] = f"Error: {e}"
                    print(f"  {prop_name}: Error - {e}")
                    
            # Test frame capture
            print("\n📸 Testing frame capture...")
            ret, frame = cap.read()
            if ret and frame is not None:
                height, width, channels = frame.shape
                print(f"  ✅ Frame captured: {width}x{height}x{channels}")
                properties['frame_capture'] = 'SUCCESS'
                properties['actual_resolution'] = f"{width}x{height}"
                properties['channels'] = channels
            else:
                print(f"  ❌ Failed to capture frame")
                properties['frame_capture'] = 'FAILED'
                
            cap.release()
            
        except Exception as e:
            print(f"❌ Error testing camera properties: {e}")
            properties['error'] = str(e)
            
        return properties
        
    def test_camera_settings(self, camera_index: int = 0) -> bool:
        """Test setting camera properties"""
        print(f"\n⚙️  TESTING CAMERA {camera_index} SETTINGS")
        print("-" * 40)
        
        try:
            cap = cv2.VideoCapture(camera_index)
            
            if not cap.isOpened():
                print(f"❌ Cannot open camera {camera_index}")
                return False
                
            # Test setting common properties
            settings_tests = [
                (cv2.CAP_PROP_FRAME_WIDTH, 640, "Width to 640"),
                (cv2.CAP_PROP_FRAME_HEIGHT, 480, "Height to 480"),
                (cv2.CAP_PROP_FPS, 30, "FPS to 30"),
                (cv2.CAP_PROP_BUFFERSIZE, 1, "Buffer size to 1"),
            ]
            
            success_count = 0
            
            for prop_id, value, description in settings_tests:
                try:
                    # Get current value
                    current = cap.get(prop_id)
                    
                    # Set new value
                    result = cap.set(prop_id, value)
                    
                    # Get new value
                    new_value = cap.get(prop_id)
                    
                    if result and abs(new_value - value) < 0.1:
                        print(f"  ✅ {description}: {current} → {new_value}")
                        success_count += 1
                    else:
                        print(f"  ⚠️  {description}: Failed (current: {current}, set: {value}, actual: {new_value})")
                        
                except Exception as e:
                    print(f"  ❌ {description}: Error - {e}")
                    
            cap.release()
            
            print(f"\n📊 Settings test result: {success_count}/{len(settings_tests)} successful")
            return success_count > 0
            
        except Exception as e:
            print(f"❌ Error testing camera settings: {e}")
            return False
            
    def run_comprehensive_test(self):
        """Run comprehensive camera diagnostic"""
        print("🚀 STARTING COMPREHENSIVE CAMERA DIAGNOSTIC")
        print("=" * 60)
        
        # System info
        self.print_system_info()
        
        # Test backends
        backend_results = self.test_camera_backends()
        
        # Scan cameras
        available_cameras = self.scan_camera_indices()
        
        if not available_cameras:
            print("\n❌ NO CAMERAS FOUND!")
            print("\nTroubleshooting suggestions:")
            print("1. Check if camera is connected properly")
            print("2. Check camera permissions")
            print("3. Close other applications using the camera")
            print("4. Try different USB ports")
            print("5. Update camera drivers")
            return False
            
        # Test first available camera in detail
        first_camera = available_cameras[0]
        properties = self.test_camera_properties(first_camera)
        settings_ok = self.test_camera_settings(first_camera)
        
        # Summary
        print("\n" + "=" * 60)
        print("📋 DIAGNOSTIC SUMMARY")
        print("=" * 60)
        print(f"Available cameras: {available_cameras}")
        print(f"Working backends: {[name for name, result in backend_results.items() if result['status'] == 'SUCCESS']}")
        print(f"Camera settings configurable: {'Yes' if settings_ok else 'No'}")
        
        if available_cameras:
            print(f"\n✅ CAMERA SYSTEM: FUNCTIONAL")
            print(f"Recommended camera index: {first_camera}")
            return True
        else:
            print(f"\n❌ CAMERA SYSTEM: NOT FUNCTIONAL")
            return False

def main():
    """Main diagnostic function"""
    diagnostic = CameraDiagnostic()
    success = diagnostic.run_comprehensive_test()
    
    if not success:
        print("\n🔧 TROUBLESHOOTING STEPS:")
        print("1. Restart the application")
        print("2. Check Windows Camera privacy settings")
        print("3. Update OpenCV: pip install --upgrade opencv-python")
        print("4. Try different camera backends in the application")
        print("5. Check if antivirus is blocking camera access")
        
    return success

class EnhancedCameraManager:
    """Enhanced camera manager with robust error handling and recovery"""

    def __init__(self):
        self.video_cap = None
        self.camera_info = {}
        self.is_running = False
        self.consecutive_failures = 0
        self.max_failures = 10
        self.preferred_backend = None
        self.preferred_index = 0

        # Status monitoring
        self.status_history = []
        self.last_successful_frame_time = None
        self.total_frames_read = 0
        self.total_failures = 0
        self.recovery_attempts = 0
        self.max_recovery_attempts = 3

        # Performance metrics
        self.fps_history = []
        self.frame_times = []
        self.last_fps_calculation = time.time()

    def initialize_camera(self, preferred_index: int = 0) -> bool:
        """Initialize camera with enhanced error handling"""
        try:
            print(f"🔄 Initializing camera {preferred_index}...")

            # Enhanced camera detection with multiple backends
            camera_backends = [
                (cv2.CAP_DSHOW, "DirectShow"),  # Windows default
                (cv2.CAP_MSMF, "Media Foundation"),  # Windows modern
                (cv2.CAP_ANY, "Any Available")  # Fallback
            ]

            camera_indices = [preferred_index, 0, 1, 2]  # Try preferred first

            for backend, backend_name in camera_backends:
                for idx in camera_indices:
                    try:
                        print(f"🔍 Trying camera {idx} with {backend_name}...")

                        # Create video capture with specific backend
                        self.video_cap = cv2.VideoCapture(idx, backend)

                        if self.video_cap.isOpened():
                            # Test frame capture
                            ret, test_frame = self.video_cap.read()
                            if ret and test_frame is not None and test_frame.size > 0:
                                height, width = test_frame.shape[:2]
                                print(f"✅ Camera {idx} working with {backend_name}!")
                                print(f"   Resolution: {width}x{height}")

                                # Store successful configuration
                                self.camera_info = {
                                    'index': idx,
                                    'backend': backend_name,
                                    'backend_id': backend,
                                    'resolution': (width, height)
                                }
                                self.preferred_backend = backend
                                self.preferred_index = idx

                                # Configure optimal settings
                                self._configure_camera_settings()

                                self.consecutive_failures = 0
                                return True
                            else:
                                print(f"⚠️ Camera {idx} opened but no frame with {backend_name}")
                                self.video_cap.release()
                                self.video_cap = None
                        else:
                            print(f"❌ Camera {idx} failed to open with {backend_name}")
                            if self.video_cap:
                                self.video_cap.release()
                            self.video_cap = None

                    except Exception as e:
                        print(f"❌ Error with camera {idx}, backend {backend_name}: {e}")
                        if self.video_cap:
                            try:
                                self.video_cap.release()
                            except:
                                pass
                            self.video_cap = None

            print("❌ No working camera configuration found!")
            return False

        except Exception as e:
            print(f"❌ Critical error initializing camera: {e}")
            return False

    def _configure_camera_settings(self):
        """Configure optimal camera settings"""
        if not self.video_cap:
            return

        try:
            # Set optimal resolution and performance settings
            settings = [
                (cv2.CAP_PROP_FRAME_WIDTH, 640),
                (cv2.CAP_PROP_FRAME_HEIGHT, 480),
                (cv2.CAP_PROP_FPS, 30),
                (cv2.CAP_PROP_BUFFERSIZE, 1),  # Reduce latency
            ]

            for prop, value in settings:
                try:
                    current = self.video_cap.get(prop)
                    success = self.video_cap.set(prop, value)
                    new_value = self.video_cap.get(prop)

                    if success:
                        print(f"  ✅ Set {prop}: {current} → {new_value}")
                    else:
                        print(f"  ⚠️ Failed to set {prop}: {current} (target: {value})")

                except Exception as e:
                    print(f"  ❌ Error setting property {prop}: {e}")

        except Exception as e:
            print(f"⚠️ Error configuring camera settings: {e}")

    def read_frame(self) -> Tuple[bool, Optional[np.ndarray]]:
        """Read frame with error handling and recovery"""
        if not self.video_cap or not self.video_cap.isOpened():
            self._update_status("disconnected", "Camera not available")
            return False, None

        try:
            frame_start_time = time.time()
            ret, frame = self.video_cap.read()

            if ret and frame is not None:
                # Update success metrics
                self.consecutive_failures = 0
                self.total_frames_read += 1
                self.last_successful_frame_time = time.time()

                # Calculate frame time and FPS
                frame_time = time.time() - frame_start_time
                self.frame_times.append(frame_time)

                # Keep only recent frame times (last 30 frames)
                if len(self.frame_times) > 30:
                    self.frame_times.pop(0)

                # Update FPS calculation every second
                current_time = time.time()
                if current_time - self.last_fps_calculation >= 1.0:
                    self._calculate_fps()
                    self.last_fps_calculation = current_time

                self._update_status("active", f"Frame read successful ({self.total_frames_read} total)")
                return True, frame
            else:
                self.consecutive_failures += 1
                self.total_failures += 1
                print(f"⚠️ Frame read failed (consecutive: {self.consecutive_failures})")

                self._update_status("error", f"Frame read failed (consecutive: {self.consecutive_failures})")

                # Attempt recovery if too many failures
                if self.consecutive_failures >= self.max_failures:
                    print("🔄 Too many failures, attempting camera recovery...")
                    if self.recover_camera():
                        return self.read_frame()  # Try again after recovery

                return False, None

        except Exception as e:
            self.consecutive_failures += 1
            self.total_failures += 1
            print(f"❌ Error reading frame: {e}")
            self._update_status("error", f"Exception during frame read: {str(e)}")
            return False, None

    def recover_camera(self) -> bool:
        """Attempt to recover camera after failures"""
        try:
            self.recovery_attempts += 1
            print(f"🔄 Attempting camera recovery (attempt {self.recovery_attempts})...")

            # Check if we've exceeded max recovery attempts
            if self.recovery_attempts > self.max_recovery_attempts:
                print(f"❌ Max recovery attempts ({self.max_recovery_attempts}) exceeded")
                self._update_status("failed", f"Max recovery attempts exceeded ({self.recovery_attempts})")
                return False

            self._update_status("recovering", f"Recovery attempt {self.recovery_attempts}")

            # Release current camera
            if self.video_cap:
                try:
                    self.video_cap.release()
                except:
                    pass
                self.video_cap = None

            # Wait a moment
            time.sleep(1)

            # Try to reinitialize with last known good configuration
            if self.preferred_backend and self.preferred_index is not None:
                try:
                    self.video_cap = cv2.VideoCapture(self.preferred_index, self.preferred_backend)
                    if self.video_cap.isOpened():
                        ret, test_frame = self.video_cap.read()
                        if ret and test_frame is not None:
                            print("✅ Camera recovery successful!")
                            self._configure_camera_settings()
                            self.consecutive_failures = 0
                            self._update_status("recovered", f"Recovery successful after {self.recovery_attempts} attempts")
                            return True
                except Exception as e:
                    print(f"⚠️ Preferred configuration failed: {e}")

            # Fall back to full reinitialization
            print("🔄 Attempting full camera reinitialization...")
            success = self.initialize_camera(self.preferred_index)

            if success:
                self._update_status("recovered", f"Full reinitialization successful after {self.recovery_attempts} attempts")
            else:
                self._update_status("failed", f"Recovery failed after {self.recovery_attempts} attempts")

            return success

        except Exception as e:
            print(f"❌ Camera recovery failed: {e}")
            self._update_status("error", f"Recovery exception: {str(e)}")
            return False

    def release(self):
        """Safely release camera resources"""
        try:
            self.is_running = False
            if self.video_cap:
                self.video_cap.release()
                self.video_cap = None
                print("✅ Camera resources released")
        except Exception as e:
            print(f"⚠️ Error releasing camera: {e}")

    def get_camera_info(self) -> Dict:
        """Get current camera information"""
        return self.camera_info.copy()

    def is_available(self) -> bool:
        """Check if camera is available and working"""
        return (self.video_cap is not None and
                self.video_cap.isOpened() and
                self.consecutive_failures < self.max_failures)

    def _update_status(self, status: str, message: str):
        """Update camera status history"""
        status_entry = {
            'timestamp': time.time(),
            'status': status,
            'message': message,
            'consecutive_failures': self.consecutive_failures,
            'total_frames': self.total_frames_read,
            'total_failures': self.total_failures
        }

        self.status_history.append(status_entry)

        # Keep only recent status entries (last 100)
        if len(self.status_history) > 100:
            self.status_history.pop(0)

    def _calculate_fps(self):
        """Calculate current FPS based on recent frame times"""
        if len(self.frame_times) > 0:
            avg_frame_time = sum(self.frame_times) / len(self.frame_times)
            if avg_frame_time > 0:
                fps = 1.0 / avg_frame_time
                self.fps_history.append(fps)

                # Keep only recent FPS values (last 30)
                if len(self.fps_history) > 30:
                    self.fps_history.pop(0)

    def get_status_report(self) -> Dict:
        """Get comprehensive status report"""
        current_time = time.time()

        # Calculate uptime
        uptime = 0
        if self.last_successful_frame_time:
            uptime = current_time - self.last_successful_frame_time

        # Calculate average FPS
        avg_fps = 0
        if len(self.fps_history) > 0:
            avg_fps = sum(self.fps_history) / len(self.fps_history)

        # Calculate success rate
        total_attempts = self.total_frames_read + self.total_failures
        success_rate = (self.total_frames_read / total_attempts * 100) if total_attempts > 0 else 0

        # Get recent status
        recent_status = "unknown"
        recent_message = "No status available"
        if len(self.status_history) > 0:
            latest = self.status_history[-1]
            recent_status = latest['status']
            recent_message = latest['message']

        return {
            'camera_info': self.camera_info.copy(),
            'status': recent_status,
            'message': recent_message,
            'is_available': self.is_available(),
            'consecutive_failures': self.consecutive_failures,
            'total_frames_read': self.total_frames_read,
            'total_failures': self.total_failures,
            'success_rate': round(success_rate, 2),
            'average_fps': round(avg_fps, 2),
            'uptime_seconds': round(uptime, 2),
            'recovery_attempts': self.recovery_attempts,
            'last_successful_frame': self.last_successful_frame_time
        }

    def get_health_score(self) -> float:
        """Calculate camera health score (0-100)"""
        if not self.is_available():
            return 0.0

        # Base score
        score = 100.0

        # Deduct for failures
        total_attempts = self.total_frames_read + self.total_failures
        if total_attempts > 0:
            failure_rate = self.total_failures / total_attempts
            score -= failure_rate * 50  # Max 50 points deduction for failures

        # Deduct for consecutive failures
        if self.consecutive_failures > 0:
            score -= min(self.consecutive_failures * 5, 30)  # Max 30 points deduction

        # Deduct for recovery attempts
        score -= min(self.recovery_attempts * 10, 20)  # Max 20 points deduction

        # Bonus for recent successful frames
        current_time = time.time()
        if self.last_successful_frame_time and (current_time - self.last_successful_frame_time) < 5.0:
            score += 5  # Bonus for recent activity

        return max(0.0, min(100.0, score))

if __name__ == "__main__":
    main()
