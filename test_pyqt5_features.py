#!/usr/bin/env python3
"""
PyQt5 AI Video Detection Tool - Feature Testing Script

This script tests all the enhanced features of the PyQt5 interface
to ensure everything is working correctly.
"""

import sys
import time
import os
from datetime import datetime

def test_imports():
    """Test that all required modules can be imported"""
    print("🧪 Testing imports...")
    
    try:
        # Test PyQt5 imports
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        from PyQt5.QtGui import QFont
        print("✅ PyQt5 imports successful")
        
        # Test GUI components
        from gui_pyqt5.login_window import LoginWindow
        from gui_pyqt5.main_window import MainWindow
        from gui_pyqt5.base_components import Colors, ModernButton, LoadingIndicator
        print("✅ GUI components imports successful")
        
        # Test AI detection modules
        from detection.custom_yolo_expression import CustomYOLOv8ExpressionDetector
        from detection.age_detection import AgeDetector
        from detection.object_detection import ObjectDetector
        from detection.anomaly_system import AnomalyDetectionSystem
        print("✅ AI detection modules imports successful")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False

def test_color_scheme():
    """Test the modern color scheme"""
    print("\n🎨 Testing color scheme...")
    
    try:
        from gui_pyqt5.base_components import Colors
        
        # Test primary colors
        assert Colors.PRIMARY == '#2563EB'
        assert Colors.SUCCESS == '#10B981'
        assert Colors.WARNING == '#F59E0B'
        assert Colors.DANGER == '#EF4444'
        print("✅ Primary colors defined correctly")
        
        # Test neutral colors
        assert Colors.WHITE == '#FFFFFF'
        assert Colors.BLACK == '#000000'
        assert Colors.GRAY_500 == '#6B7280'
        print("✅ Neutral colors defined correctly")
        
        # Test background colors
        assert Colors.BACKGROUND == '#F8FAFC'
        print("✅ Background colors defined correctly")
        
        return True
        
    except Exception as e:
        print(f"❌ Color scheme error: {e}")
        return False

def test_modern_button():
    """Test the ModernButton component"""
    print("\n🔘 Testing ModernButton component...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from gui_pyqt5.base_components import ModernButton
        
        # Create QApplication if it doesn't exist
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # Test different button styles
        primary_btn = ModernButton("Primary", style='primary')
        success_btn = ModernButton("Success", style='success')
        warning_btn = ModernButton("Warning", style='warning')
        danger_btn = ModernButton("Danger", style='danger')
        outline_btn = ModernButton("Outline", style='outline')
        
        print("✅ ModernButton styles created successfully")
        
        # Test different sizes
        small_btn = ModernButton("Small", size='small')
        medium_btn = ModernButton("Medium", size='medium')
        large_btn = ModernButton("Large", size='large')
        
        print("✅ ModernButton sizes created successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ ModernButton error: {e}")
        return False

def test_ai_modules():
    """Test AI detection modules initialization"""
    print("\n🤖 Testing AI modules...")
    
    try:
        # Test expression detector
        from detection.custom_yolo_expression import CustomYOLOv8ExpressionDetector
        if os.path.exists('models/emotion_detection_83.6_percent.pt'):
            print("✅ Expression detection model found")
        else:
            print("⚠️ Expression detection model not found")
        
        # Test age detector
        from detection.age_detection import AgeDetector
        if os.path.exists('models/age_deploy.prototxt') and os.path.exists('models/age_net.caffemodel'):
            print("✅ Age detection models found")
        else:
            print("⚠️ Age detection models not found")
        
        # Test object detector
        from detection.object_detection import ObjectDetector
        print("✅ Object detection module available")
        
        # Test anomaly system
        from detection.anomaly_system import AnomalyDetectionSystem
        if os.path.exists('models/yolov3.weights') and os.path.exists('models/yolov3.cfg'):
            print("✅ Anomaly detection models found")
        else:
            print("⚠️ Anomaly detection models not found")
        
        return True
        
    except Exception as e:
        print(f"❌ AI modules error: {e}")
        return False

def test_database_integration():
    """Test database integration"""
    print("\n🗄️ Testing database integration...")
    
    try:
        from utils.database_integration import get_database
        
        # Test database connection
        db = get_database()
        if db:
            print("✅ Database connection successful")
        else:
            print("⚠️ Database connection failed")
        
        return True
        
    except Exception as e:
        print(f"❌ Database integration error: {e}")
        return False

def test_directory_structure():
    """Test that all required directories exist"""
    print("\n📁 Testing directory structure...")
    
    required_dirs = [
        'gui_pyqt5',
        'detection',
        'models',
        'utils',
        'data',
        'recordings',
        'snapshots',
        'reports'
    ]
    
    missing_dirs = []
    for dir_name in required_dirs:
        if not os.path.exists(dir_name):
            missing_dirs.append(dir_name)
    
    if missing_dirs:
        print(f"⚠️ Missing directories: {missing_dirs}")
    else:
        print("✅ All required directories exist")
    
    return len(missing_dirs) == 0

def run_comprehensive_test():
    """Run all tests"""
    print("🛡️ AI Video Detection Tool - PyQt5 Feature Testing")
    print("=" * 60)
    
    tests = [
        ("Import Test", test_imports),
        ("Color Scheme Test", test_color_scheme),
        ("ModernButton Test", test_modern_button),
        ("AI Modules Test", test_ai_modules),
        ("Database Test", test_database_integration),
        ("Directory Structure Test", test_directory_structure)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! PyQt5 interface is ready for use.")
        return True
    else:
        print("⚠️ Some tests failed. Please check the issues above.")
        return False

if __name__ == "__main__":
    success = run_comprehensive_test()
    sys.exit(0 if success else 1)
