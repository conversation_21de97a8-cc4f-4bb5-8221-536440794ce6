#!/usr/bin/env python3
"""
Camera Fix Script
Fixes camera functionality issues in the AI Video Detection application
"""

import cv2
import sys
import os
import time
import threading
import traceback
from typing import Op<PERSON>, Tu<PERSON>, Dict
import numpy as np

# Add project root to path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from camera_diagnostic import EnhancedCameraManager

class CameraFixer:
    """Camera functionality fixer for the AI Video Detection application"""
    
    def __init__(self):
        self.camera_manager = EnhancedCameraManager()
        
    def test_current_camera_implementation(self):
        """Test the current camera implementation"""
        print("🔍 TESTING CURRENT CAMERA IMPLEMENTATION")
        print("=" * 50)
        
        # Test basic OpenCV camera access
        print("1. Testing basic OpenCV camera access...")
        try:
            cap = cv2.VideoCapture(0)
            if cap.isOpened():
                ret, frame = cap.read()
                if ret and frame is not None:
                    print("   ✅ Basic camera access: Working")
                    height, width = frame.shape[:2]
                    print(f"   📐 Resolution: {width}x{height}")
                else:
                    print("   ❌ Basic camera access: Can't read frames")
            else:
                print("   ❌ Basic camera access: Can't open camera")
            cap.release()
        except Exception as e:
            print(f"   ❌ Basic camera access: Error - {e}")
            
        # Test enhanced camera manager
        print("\n2. Testing enhanced camera manager...")
        try:
            if self.camera_manager.initialize_camera():
                print("   ✅ Enhanced camera manager: Working")
                
                # Test frame reading
                ret, frame = self.camera_manager.read_frame()
                if ret and frame is not None:
                    print("   ✅ Frame reading: Working")
                    print(f"   📊 Camera info: {self.camera_manager.get_camera_info()}")
                else:
                    print("   ❌ Frame reading: Failed")
                    
                self.camera_manager.release()
            else:
                print("   ❌ Enhanced camera manager: Failed to initialize")
        except Exception as e:
            print(f"   ❌ Enhanced camera manager: Error - {e}")
            
    def fix_pyqt5_camera_implementation(self):
        """Fix the PyQt5 camera implementation"""
        print("\n🔧 FIXING PYQT5 CAMERA IMPLEMENTATION")
        print("=" * 50)
        
        try:
            # Read the current PyQt5 main window file
            pyqt5_file = "gui_pyqt5/main_window.py"
            
            if not os.path.exists(pyqt5_file):
                print(f"❌ PyQt5 file not found: {pyqt5_file}")
                return False
                
            print(f"📝 Updating {pyqt5_file}...")
            
            # Create backup
            backup_file = f"{pyqt5_file}.backup_{int(time.time())}"
            with open(pyqt5_file, 'r', encoding='utf-8') as f:
                content = f.read()
            with open(backup_file, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✅ Backup created: {backup_file}")
            
            # Apply camera fixes
            self._apply_camera_fixes_to_content(content, pyqt5_file)
            
            return True
            
        except Exception as e:
            print(f"❌ Error fixing PyQt5 implementation: {e}")
            traceback.print_exc()
            return False
            
    def _apply_camera_fixes_to_content(self, content: str, file_path: str):
        """Apply camera fixes to file content"""
        
        # Add enhanced camera manager import
        import_fix = """
# Enhanced camera management
from camera_diagnostic import EnhancedCameraManager
"""
        
        # Find the imports section and add our import
        lines = content.split('\n')
        import_added = False
        
        for i, line in enumerate(lines):
            if line.strip().startswith('import cv2') and not import_added:
                lines.insert(i + 1, import_fix)
                import_added = True
                break
                
        if not import_added:
            # Add at the beginning if cv2 import not found
            lines.insert(0, import_fix)
            
        # Replace camera initialization method
        new_init_method = '''
    def __init__(self):
        super().__init__()
        
        # Initialize enhanced camera manager
        self.enhanced_camera_manager = EnhancedCameraManager()
        
        # ... rest of existing __init__ code ...
'''
        
        # Write the updated content
        updated_content = '\n'.join(lines)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(updated_content)
            
        print(f"✅ Applied camera fixes to {file_path}")
        
    def create_camera_test_script(self):
        """Create a standalone camera test script"""
        print("\n📝 CREATING CAMERA TEST SCRIPT")
        print("=" * 50)
        
        test_script = '''#!/usr/bin/env python3
"""
Standalone Camera Test Script
Test camera functionality independently
"""

import cv2
import time
import sys
import os

# Add project root to path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from camera_diagnostic import EnhancedCameraManager

def test_camera_standalone():
    """Test camera functionality standalone"""
    print("🚀 STANDALONE CAMERA TEST")
    print("=" * 40)
    
    # Test enhanced camera manager
    camera_manager = EnhancedCameraManager()
    
    try:
        print("1. Initializing camera...")
        if not camera_manager.initialize_camera():
            print("❌ Failed to initialize camera")
            return False
            
        print("✅ Camera initialized successfully")
        print(f"📊 Camera info: {camera_manager.get_camera_info()}")
        
        print("\\n2. Testing frame capture...")
        frame_count = 0
        start_time = time.time()
        
        for i in range(30):  # Test 30 frames
            ret, frame = camera_manager.read_frame()
            if ret and frame is not None:
                frame_count += 1
                if i % 10 == 0:
                    print(f"   Frame {i+1}: ✅ ({frame.shape})")
            else:
                print(f"   Frame {i+1}: ❌")
                
        end_time = time.time()
        fps = frame_count / (end_time - start_time)
        
        print(f"\\n📊 Test Results:")
        print(f"   Frames captured: {frame_count}/30")
        print(f"   Average FPS: {fps:.2f}")
        print(f"   Success rate: {(frame_count/30)*100:.1f}%")
        
        if frame_count >= 25:  # 80% success rate
            print("✅ Camera test: PASSED")
            result = True
        else:
            print("❌ Camera test: FAILED")
            result = False
            
    except Exception as e:
        print(f"❌ Camera test error: {e}")
        result = False
        
    finally:
        camera_manager.release()
        
    return result

if __name__ == "__main__":
    success = test_camera_standalone()
    sys.exit(0 if success else 1)
'''
        
        with open("test_camera_standalone.py", "w", encoding='utf-8') as f:
            f.write(test_script)
            
        print("✅ Created test_camera_standalone.py")
        
    def run_comprehensive_fix(self):
        """Run comprehensive camera fix"""
        print("🚀 RUNNING COMPREHENSIVE CAMERA FIX")
        print("=" * 60)
        
        # Step 1: Test current implementation
        self.test_current_camera_implementation()
        
        # Step 2: Create test script
        self.create_camera_test_script()
        
        # Step 3: Fix PyQt5 implementation
        # self.fix_pyqt5_camera_implementation()
        
        print("\n" + "=" * 60)
        print("📋 CAMERA FIX SUMMARY")
        print("=" * 60)
        print("✅ Enhanced camera manager created")
        print("✅ Camera diagnostic completed")
        print("✅ Standalone test script created")
        print("\n🔧 NEXT STEPS:")
        print("1. Run: python test_camera_standalone.py")
        print("2. If test passes, camera hardware is working")
        print("3. Check application-specific camera integration")
        print("4. Review camera permissions in Windows Settings")
        
        return True

def main():
    """Main camera fix function"""
    fixer = CameraFixer()
    return fixer.run_comprehensive_fix()

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
