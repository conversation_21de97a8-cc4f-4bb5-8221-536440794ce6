"""
Interactive Components for Professional AI Video Detection Interface

This module provides advanced interactive components including:
- Real-time status indicators
- Progress tracking systems
- Interactive feedback elements
- Professional animations and transitions
"""

import sys
from PyQt5.QtWidgets import (
    QWidget, QLabel, QFrame, QVBoxLayout, QHBoxLayout, 
    QProgressBar, QGraphicsOpacityEffect, QApplication
)
from PyQt5.QtCore import Qt, QTimer, QPropertyAnimation, QEasingCurve, pyqtSignal, QRect
from PyQt5.QtGui import QPainter, QColor, QFont, QLinearGradient, QPen

from .base_components import Colors, Typography, ModernButton, StyledLabel


class RealTimeStatusPanel(QFrame):
    """
    Real-time status panel for AI detection operations
    """
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        self.setFrameStyle(QFrame.NoFrame)
        self.setStyleSheet(f"""
            QFrame {{
                background-color: {Colors.WHITE};
                border: 1px solid {Colors.GRAY_200};
                border-radius: 12px;
                padding: 16px;
            }}
        """)
        
        # Create layout
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 16, 20, 16)
        layout.setSpacing(12)
        
        # Title
        title = StyledLabel("🤖 AI Detection Status", size_type='heading', weight='bold')
        layout.addWidget(title)
        
        # Status indicators
        self.camera_status = StatusIndicatorRow("📹 Camera", "Initializing...")
        self.ai_status = StatusIndicatorRow("🧠 AI Models", "Loading...")
        self.detection_status = StatusIndicatorRow("🔍 Detection", "Ready")
        self.recording_status = StatusIndicatorRow("📼 Recording", "Stopped")
        
        layout.addWidget(self.camera_status)
        layout.addWidget(self.ai_status)
        layout.addWidget(self.detection_status)
        layout.addWidget(self.recording_status)
        
        # Performance metrics
        layout.addWidget(self.create_separator())
        
        perf_title = StyledLabel("📊 Performance", size_type='body', weight='bold')
        layout.addWidget(perf_title)
        
        self.fps_indicator = MetricIndicator("FPS", "0.0", Colors.INFO)
        self.processing_time = MetricIndicator("Processing", "0ms", Colors.SUCCESS)
        self.detection_count = MetricIndicator("Detections", "0", Colors.PRIMARY)
        
        metrics_layout = QHBoxLayout()
        metrics_layout.addWidget(self.fps_indicator)
        metrics_layout.addWidget(self.processing_time)
        metrics_layout.addWidget(self.detection_count)
        
        layout.addLayout(metrics_layout)
    
    def create_separator(self):
        """Create a visual separator"""
        separator = QFrame()
        separator.setFrameStyle(QFrame.HLine)
        separator.setStyleSheet(f"color: {Colors.GRAY_200};")
        separator.setFixedHeight(1)
        return separator
    
    def update_camera_status(self, status, message):
        """Update camera status"""
        self.camera_status.update_status(status, message)
    
    def update_ai_status(self, status, message):
        """Update AI models status"""
        self.ai_status.update_status(status, message)
    
    def update_detection_status(self, status, message):
        """Update detection status"""
        self.detection_status.update_status(status, message)
    
    def update_recording_status(self, status, message):
        """Update recording status"""
        self.recording_status.update_status(status, message)
    
    def update_metrics(self, fps=None, processing_time=None, detection_count=None):
        """Update performance metrics"""
        if fps is not None:
            self.fps_indicator.update_value(f"{fps:.1f}")
        if processing_time is not None:
            self.processing_time.update_value(f"{processing_time:.0f}ms")
        if detection_count is not None:
            self.detection_count.update_value(str(detection_count))


class StatusIndicatorRow(QWidget):
    """
    Individual status indicator row with icon and animated status
    """
    
    def __init__(self, label, initial_status="Ready", parent=None):
        super().__init__(parent)
        
        layout = QHBoxLayout(self)
        layout.setContentsMargins(0, 4, 0, 4)
        layout.setSpacing(12)
        
        # Label
        self.label = StyledLabel(label, size_type='body', weight='medium')
        self.label.setFixedWidth(120)
        layout.addWidget(self.label)
        
        # Status indicator
        self.status_dot = StatusDot()
        layout.addWidget(self.status_dot)
        
        # Status text
        self.status_text = StyledLabel(initial_status, size_type='body')
        layout.addWidget(self.status_text)
        
        layout.addStretch()
        
        # Initialize with neutral status
        self.update_status('neutral', initial_status)
    
    def update_status(self, status_type, message):
        """Update the status indicator"""
        self.status_dot.set_status(status_type)
        self.status_text.setText(message)
        
        # Update text color based on status
        color_map = {
            'success': Colors.SUCCESS_DARK,
            'warning': Colors.WARNING_DARK,
            'error': Colors.DANGER_DARK,
            'info': Colors.INFO,
            'neutral': Colors.GRAY_600
        }
        
        color = color_map.get(status_type, Colors.GRAY_600)
        self.status_text.setStyleSheet(f"color: {color};")


class StatusDot(QWidget):
    """
    Animated status dot indicator
    """
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        self.setFixedSize(12, 12)
        self.status = 'neutral'
        self.pulse_opacity = 1.0
        
        # Animation timer for pulsing effect
        self.pulse_timer = QTimer()
        self.pulse_timer.timeout.connect(self.update_pulse)
        self.pulse_direction = -1
    
    def set_status(self, status):
        """Set the status and start appropriate animation"""
        self.status = status
        
        if status in ['success', 'info']:
            # Solid indicator for stable states
            self.pulse_timer.stop()
            self.pulse_opacity = 1.0
        elif status in ['warning', 'error']:
            # Pulsing for attention-requiring states
            self.pulse_timer.start(100)
        else:
            # Neutral state
            self.pulse_timer.stop()
            self.pulse_opacity = 0.7
        
        self.update()
    
    def update_pulse(self):
        """Update pulse animation"""
        self.pulse_opacity += self.pulse_direction * 0.1
        
        if self.pulse_opacity <= 0.3:
            self.pulse_direction = 1
        elif self.pulse_opacity >= 1.0:
            self.pulse_direction = -1
        
        self.update()
    
    def paintEvent(self, event):
        """Paint the status dot"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # Color mapping
        color_map = {
            'success': Colors.SUCCESS,
            'warning': Colors.WARNING,
            'error': Colors.DANGER,
            'info': Colors.INFO,
            'neutral': Colors.GRAY_400
        }
        
        color = QColor(color_map.get(self.status, Colors.GRAY_400))
        color.setAlphaF(self.pulse_opacity)
        
        # Draw the dot
        painter.setBrush(color)
        painter.setPen(Qt.NoPen)
        painter.drawEllipse(2, 2, 8, 8)


class MetricIndicator(QFrame):
    """
    Professional metric indicator with value and label
    """
    
    def __init__(self, label, value, color=Colors.PRIMARY, parent=None):
        super().__init__(parent)
        
        self.color = color
        
        self.setFrameStyle(QFrame.NoFrame)
        self.setStyleSheet(f"""
            QFrame {{
                background-color: {Colors.GRAY_50};
                border: 1px solid {Colors.GRAY_200};
                border-radius: 8px;
                padding: 8px;
            }}
        """)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(12, 8, 12, 8)
        layout.setSpacing(4)
        
        # Value (large)
        self.value_label = StyledLabel(value, size_type='heading', weight='bold')
        self.value_label.setAlignment(Qt.AlignCenter)
        self.value_label.setStyleSheet(f"color: {color};")
        layout.addWidget(self.value_label)
        
        # Label (small)
        label_widget = StyledLabel(label, size_type='small')
        label_widget.setAlignment(Qt.AlignCenter)
        label_widget.setStyleSheet(f"color: {Colors.GRAY_600};")
        layout.addWidget(label_widget)
    
    def update_value(self, new_value):
        """Update the metric value with animation"""
        self.value_label.setText(new_value)
        
        # Brief highlight animation
        self.animate_highlight()
    
    def animate_highlight(self):
        """Brief highlight animation when value changes"""
        original_style = self.styleSheet()
        
        # Highlight
        self.setStyleSheet(f"""
            QFrame {{
                background-color: {self.color};
                border: 1px solid {self.color};
                border-radius: 8px;
                padding: 8px;
            }}
        """)
        
        # Reset after brief delay
        QTimer.singleShot(150, lambda: self.setStyleSheet(original_style))


class ProgressTracker(QFrame):
    """
    Advanced progress tracker for AI operations
    """
    
    def __init__(self, title="Operation Progress", parent=None):
        super().__init__(parent)
        
        self.setFrameStyle(QFrame.NoFrame)
        self.setStyleSheet(f"""
            QFrame {{
                background-color: {Colors.WHITE};
                border: 1px solid {Colors.GRAY_200};
                border-radius: 12px;
                padding: 16px;
            }}
        """)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 16, 20, 16)
        layout.setSpacing(12)
        
        # Title
        self.title_label = StyledLabel(title, size_type='heading', weight='bold')
        layout.addWidget(self.title_label)
        
        # Progress bar
        self.progress_bar = EnhancedProgressBar()
        layout.addWidget(self.progress_bar)
        
        # Status text
        self.status_label = StyledLabel("Ready to start...", size_type='body')
        self.status_label.setStyleSheet(f"color: {Colors.GRAY_600};")
        layout.addWidget(self.status_label)
        
        # Time estimate
        self.time_label = StyledLabel("", size_type='small')
        self.time_label.setStyleSheet(f"color: {Colors.GRAY_500};")
        layout.addWidget(self.time_label)
    
    def update_progress(self, value, status_text="", time_estimate=""):
        """Update progress with optional status and time estimate"""
        self.progress_bar.set_progress(value)
        
        if status_text:
            self.status_label.setText(status_text)
        
        if time_estimate:
            self.time_label.setText(f"⏱️ {time_estimate}")
        
        # Update title with percentage
        if value > 0:
            self.title_label.setText(f"Operation Progress ({value:.0f}%)")


class EnhancedProgressBar(QWidget):
    """
    Enhanced progress bar with gradient and smooth animation
    """
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        self.progress = 0
        self.target_progress = 0
        self.setFixedHeight(8)
        
        # Animation timer
        self.animation_timer = QTimer()
        self.animation_timer.timeout.connect(self.animate_progress)
        
        self.setStyleSheet("background: transparent;")
    
    def set_progress(self, value):
        """Set progress value with smooth animation"""
        self.target_progress = max(0, min(100, value))
        
        if not self.animation_timer.isActive():
            self.animation_timer.start(16)  # ~60 FPS
    
    def animate_progress(self):
        """Animate progress towards target"""
        diff = self.target_progress - self.progress
        
        if abs(diff) < 0.5:
            self.progress = self.target_progress
            self.animation_timer.stop()
        else:
            self.progress += diff * 0.1  # Smooth easing
        
        self.update()
    
    def paintEvent(self, event):
        """Paint the enhanced progress bar"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # Background
        bg_rect = QRect(0, 0, self.width(), self.height())
        painter.fillRect(bg_rect, QColor(Colors.GRAY_200))
        
        # Progress with gradient
        if self.progress > 0:
            progress_width = int((self.progress / 100) * self.width())
            progress_rect = QRect(0, 0, progress_width, self.height())
            
            # Create gradient
            gradient = QLinearGradient(0, 0, progress_width, 0)
            gradient.setColorAt(0, QColor(Colors.PRIMARY))
            gradient.setColorAt(0.5, QColor(Colors.PRIMARY_LIGHT))
            gradient.setColorAt(1, QColor(Colors.INFO))
            
            painter.fillRect(progress_rect, gradient)
