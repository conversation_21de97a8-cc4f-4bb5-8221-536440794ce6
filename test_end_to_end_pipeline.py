#!/usr/bin/env python3
"""
End-to-End Pipeline Test for AI Video Detection Application
Tests the complete pipeline from camera input to result display with real detection scenarios
"""

import sys
import os
import time
import threading
import cv2
import numpy as np
from datetime import datetime
from typing import Dict, List, Tuple

def test_camera_to_detection_pipeline():
    """Test complete camera to detection pipeline"""
    print("🎬 Testing Camera to Detection Pipeline...")
    
    try:
        # Initialize camera
        cap = cv2.VideoCapture(0)
        if not cap.isOpened():
            print("❌ Camera not available for pipeline test")
            return False
        
        # Configure camera
        cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
        cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
        cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
        
        # Initialize detection systems
        from detection.custom_yolo_expression import CustomYOLOv8ExpressionDetector
        from detection.age_detection import AgeDetector
        from detection.object_detection import ObjectDetector
        from detection.anomaly_system import AnomalyDetectionSystem
        
        print("🤖 Loading AI detection models...")
        expression_detector = CustomYOLOv8ExpressionDetector()
        age_detector = AgeDetector()
        object_detector = ObjectDetector()
        anomaly_system = AnomalyDetectionSystem()
        
        # Initialize optimizers
        from detection_pipeline_optimizer import get_detection_optimizer
        from performance_optimizer import get_performance_optimizer
        
        detection_optimizer = get_detection_optimizer()
        performance_optimizer = get_performance_optimizer()
        
        print("✅ All systems initialized, starting pipeline test...")
        
        # Test pipeline for 30 seconds
        test_duration = 30
        start_time = time.time()
        frame_count = 0
        detection_results = []
        
        while time.time() - start_time < test_duration:
            ret, frame = cap.read()
            if not ret:
                print("❌ Failed to read frame")
                break
            
            frame_count += 1
            
            # Apply performance optimization
            optimized_frame, opt_info = performance_optimizer.optimize_frame_processing(frame)
            
            # Skip frame if optimization suggests it
            if opt_info.get('skipped', False):
                continue
            
            # Test detection every 10 frames
            if frame_count % 10 == 0:
                frame_results = {}
                
                # Test facial expression detection
                try:
                    success, expr_result = detection_optimizer.optimize_detection_call(
                        'facial_expression', 
                        expression_detector.detect_expression, 
                        optimized_frame
                    )
                    if success and expr_result:
                        frame_results['expression'] = expr_result
                except Exception as e:
                    print(f"⚠️ Expression detection error: {e}")
                
                # Test age detection
                try:
                    success, age_result = detection_optimizer.optimize_detection_call(
                        'age_detection',
                        age_detector.detect_age,
                        optimized_frame
                    )
                    if success and age_result:
                        frame_results['age'] = age_result
                except Exception as e:
                    print(f"⚠️ Age detection error: {e}")
                
                # Test object detection
                try:
                    success, obj_result = detection_optimizer.optimize_detection_call(
                        'object_detection',
                        object_detector.detect_objects,
                        optimized_frame
                    )
                    if success and obj_result:
                        frame_results['objects'] = obj_result
                except Exception as e:
                    print(f"⚠️ Object detection error: {e}")
                
                # Test anomaly detection
                try:
                    success, anomaly_result = detection_optimizer.optimize_detection_call(
                        'anomaly_detection',
                        anomaly_system.process_frame,
                        optimized_frame
                    )
                    if success and anomaly_result:
                        frame_results['anomaly'] = anomaly_result
                except Exception as e:
                    print(f"⚠️ Anomaly detection error: {e}")
                
                if frame_results:
                    detection_results.append({
                        'frame': frame_count,
                        'timestamp': time.time(),
                        'results': frame_results,
                        'optimization': opt_info
                    })
                    
                    # Print progress
                    if len(detection_results) % 5 == 0:
                        print(f"📊 Processed {len(detection_results)} detection cycles...")
        
        cap.release()
        
        # Analyze results
        print(f"\n📊 Pipeline Test Results:")
        print(f"   Total frames: {frame_count}")
        print(f"   Detection cycles: {len(detection_results)}")
        print(f"   Test duration: {test_duration}s")
        print(f"   Average FPS: {frame_count / test_duration:.1f}")
        
        # Count successful detections by type
        detection_counts = {
            'expression': 0,
            'age': 0,
            'objects': 0,
            'anomaly': 0
        }
        
        for result in detection_results:
            for det_type in detection_counts:
                if det_type in result['results']:
                    detection_counts[det_type] += 1
        
        print(f"   Successful detections:")
        for det_type, count in detection_counts.items():
            success_rate = (count / len(detection_results)) * 100 if detection_results else 0
            print(f"     {det_type}: {count}/{len(detection_results)} ({success_rate:.1f}%)")
        
        # Get performance report
        perf_report = performance_optimizer.get_performance_report()
        print(f"   Performance metrics:")
        print(f"     CPU usage: {perf_report['metrics']['cpu_usage']['current']:.1f}%")
        print(f"     Memory usage: {perf_report['metrics']['memory_usage']['current']:.1f}%")
        print(f"     Frame rate: {perf_report['metrics']['frame_rate']['current']:.1f} FPS")
        
        # Cleanup
        detection_optimizer.cleanup()
        performance_optimizer.cleanup()
        
        print("✅ End-to-end pipeline test completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Pipeline test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_gui_integration():
    """Test GUI integration with real detection pipeline"""
    print("\n🖥️ Testing GUI Integration...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from gui_pyqt5.main_window import EnhancedMainWindow
        
        # Create application
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # Create main window
        main_window = EnhancedMainWindow()
        
        # Test camera initialization
        print("📷 Testing camera initialization through GUI...")
        camera_success = main_window.start_video_capture()
        
        if camera_success:
            print("✅ Camera started successfully through GUI")
            
            # Let it run for a few seconds
            time.sleep(5)
            
            # Test detection toggles
            print("🔧 Testing detection toggles...")
            main_window.real_time_age = True
            main_window.real_time_objects = True
            main_window.real_time_anomaly = True
            
            # Let detections run
            time.sleep(5)
            
            # Stop camera
            main_window.stop_video_capture()
            print("✅ Camera stopped successfully")
            
        else:
            print("❌ Camera initialization failed through GUI")
            return False
        
        print("✅ GUI integration test completed!")
        return True
        
    except Exception as e:
        print(f"❌ GUI integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_error_recovery():
    """Test error handling and recovery mechanisms"""
    print("\n🛡️ Testing Error Recovery...")
    
    try:
        from error_handling_system import get_error_handler, ErrorCategory, ErrorSeverity
        
        error_handler = get_error_handler()
        
        # Test various error scenarios
        test_errors = [
            (Exception("Test camera error"), ErrorCategory.CAMERA, ErrorSeverity.HIGH),
            (Exception("Test model loading error"), ErrorCategory.MODEL_LOADING, ErrorSeverity.MEDIUM),
            (Exception("Test memory error"), ErrorCategory.MEMORY, ErrorSeverity.HIGH),
            (Exception("Test file I/O error"), ErrorCategory.FILE_IO, ErrorSeverity.LOW),
        ]
        
        recovery_results = []
        
        for error, category, severity in test_errors:
            print(f"🧪 Testing {category.value} error recovery...")
            
            recovered = error_handler.handle_error(
                error, category, severity,
                context={'test': True, 'error_type': category.value}
            )
            
            recovery_results.append({
                'category': category.value,
                'recovered': recovered,
                'severity': severity.value
            })
            
            print(f"   Recovery result: {'✅ Success' if recovered else '❌ Failed'}")
        
        # Get error summary
        summary = error_handler.get_error_summary()
        print(f"\n📊 Error Recovery Results:")
        print(f"   Total errors handled: {summary['total_errors']}")
        print(f"   Recovery attempts: {len(recovery_results)}")
        
        successful_recoveries = sum(1 for r in recovery_results if r['recovered'])
        recovery_rate = (successful_recoveries / len(recovery_results)) * 100
        print(f"   Recovery rate: {successful_recoveries}/{len(recovery_results)} ({recovery_rate:.1f}%)")
        
        print("✅ Error recovery test completed!")
        return True
        
    except Exception as e:
        print(f"❌ Error recovery test failed: {e}")
        return False

def test_performance_under_load():
    """Test system performance under load"""
    print("\n⚡ Testing Performance Under Load...")
    
    try:
        from performance_optimizer import get_performance_optimizer
        
        optimizer = get_performance_optimizer()
        
        # Create synthetic load
        print("🔥 Creating synthetic processing load...")
        
        # Generate test frames
        test_frames = []
        for i in range(100):
            frame = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
            test_frames.append(frame)
        
        # Process frames rapidly
        start_time = time.time()
        processed_frames = 0
        
        for frame in test_frames:
            optimized_frame, opt_info = optimizer.optimize_frame_processing(frame)
            
            if not opt_info.get('skipped', False):
                processed_frames += 1
            
            # Simulate some processing delay
            time.sleep(0.01)
        
        processing_time = time.time() - start_time
        
        # Get performance report
        perf_report = optimizer.get_performance_report()
        
        print(f"📊 Performance Under Load Results:")
        print(f"   Total frames: {len(test_frames)}")
        print(f"   Processed frames: {processed_frames}")
        print(f"   Skipped frames: {len(test_frames) - processed_frames}")
        print(f"   Processing time: {processing_time:.2f}s")
        print(f"   Effective FPS: {processed_frames / processing_time:.1f}")
        print(f"   CPU usage: {perf_report['metrics']['cpu_usage']['current']:.1f}%")
        print(f"   Memory usage: {perf_report['metrics']['memory_usage']['current']:.1f}%")
        
        # Check if performance is acceptable
        effective_fps = processed_frames / processing_time
        if effective_fps >= 15:  # Minimum acceptable FPS
            print("✅ Performance under load test passed!")
            return True
        else:
            print("⚠️ Performance under load test shows degradation")
            return False
        
    except Exception as e:
        print(f"❌ Performance under load test failed: {e}")
        return False

def main():
    """Run complete end-to-end testing"""
    print("🛡️ AI Video Detection - End-to-End Pipeline Test")
    print("=" * 70)
    print(f"📅 Test Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🐍 Python: {sys.version}")
    print(f"📁 Working Directory: {os.getcwd()}")
    print()
    
    tests = [
        ("Camera to Detection Pipeline", test_camera_to_detection_pipeline),
        ("GUI Integration", test_gui_integration),
        ("Error Recovery", test_error_recovery),
        ("Performance Under Load", test_performance_under_load)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} test PASSED")
            else:
                print(f"❌ {test_name} test FAILED")
        except Exception as e:
            print(f"💥 {test_name} test CRASHED: {e}")
    
    print("\n" + "=" * 70)
    print(f"📊 Final Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! System is fully operational and ready for production use.")
        print("\n🚀 The AI Video Detection application is now:")
        print("   ✅ Camera integration working reliably")
        print("   ✅ All AI detection modules functioning")
        print("   ✅ Error handling and recovery operational")
        print("   ✅ Performance optimization active")
        print("   ✅ GUI integration complete")
        print("   ✅ End-to-end pipeline validated")
        return True
    else:
        print("⚠️ Some tests failed. Please review the issues above.")
        print(f"   {passed} tests passed, {total - passed} tests failed")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
